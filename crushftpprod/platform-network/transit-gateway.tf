
data "aws_ec2_transit_gateway" "network" {
  id = "tgw-03fd0d3acdb4ba683"
}

resource "aws_ec2_transit_gateway_vpc_attachment" "network" {
  subnet_ids         = module.vpc.private_subnets
  transit_gateway_id = data.aws_ec2_transit_gateway.network.id
  vpc_id             = module.vpc.vpc_id

  tags = {
    "Name" = "network-${var.environment_name}-${var.module_name}-tgw"
  }
}

# private subnet to network account vpc
resource "aws_route" "tgw_network_vpc" {
  route_table_id         = module.vpc.private_route_table_ids.0
  destination_cidr_block = "172.30.24.0/21"
  transit_gateway_id     = data.aws_ec2_transit_gateway.network.id
}


# private subnet to the havprodserv vpc
resource "aws_route" "tgw_private_havprodserv" {
  route_table_id         = module.vpc.private_route_table_ids.0
  destination_cidr_block = "172.31.160.0/20"
  transit_gateway_id     = data.aws_ec2_transit_gateway.network.id
}

# private subnet to the aw-blg-digital-prod platform-network vpc
resource "aws_route" "tgw_private_aw_blg_digital_prod" {
  route_table_id         = module.vpc.private_route_table_ids.0
  destination_cidr_block = "172.25.128.0/19"
  transit_gateway_id     = data.aws_ec2_transit_gateway.network.id
}