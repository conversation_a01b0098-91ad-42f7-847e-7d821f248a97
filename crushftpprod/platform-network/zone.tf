resource "aws_route53_zone" "crushftp" {
  name = "crushftpserver.com"
}

output "zone_id" {
  description = "crushftpserver.com zone ID"
  value       = aws_route53_zone.crushftp.zone_id
}

resource "aws_route53domains_registered_domain" "crushftp" {
  provider = aws.us-east-1

  domain_name = "crushftpserver.com"

  auto_renew    = true
  transfer_lock = true

  admin_privacy      = true
  registrant_privacy = true
  tech_privacy       = true

  admin_contact {
    contact_type      = "COMPANY"
    first_name        = "Haven"
    last_name         = "Platform"
    organization_name = "Bourne Leisure Limited"
    address_line_1    = "1 Park Lane"
    city              = "Hemel Hempstead"
    country_code      = "GB"
    zip_code          = "HP2 4YL"
    phone_number      = "+44.**********"
    email             = "<EMAIL>"
  }

  registrant_contact {
    contact_type      = "COMPANY"
    first_name        = "Haven"
    last_name         = "Platform"
    organization_name = "Bourne Leisure Limited"
    address_line_1    = "1 Park Lane"
    city              = "Hemel Hempstead"
    country_code      = "GB"
    zip_code          = "HP2 4YL"
    phone_number      = "+44.**********"
    email             = "<EMAIL>"
  }

  tech_contact {
    contact_type      = "COMPANY"
    first_name        = "Haven"
    last_name         = "Platform"
    organization_name = "Bourne Leisure Limited"
    address_line_1    = "1 Park Lane"
    city              = "Hemel Hempstead"
    country_code      = "GB"
    zip_code          = "HP2 4YL"
    phone_number      = "+44.**********"
    email             = "<EMAIL>"
  }

  dynamic "name_server" {
    for_each = aws_route53_zone.crushftp.name_servers

    content {
      name = name_server.value
    }
  }

  tags = merge(local.resource_tags, {
    "blg:access" = "restricted"
  })
}
