/*
 * Module specific locals
 *
 */

provider "aws" {
  profile = var.aws_profile
  region  = "us-east-1"
  alias   = "us-east-1"
}

locals {
  azs             = ["${var.aws_region}a", "${var.aws_region}b", "${var.aws_region}c"]
  public_subnets  = ["***********/26"]
  private_subnets = ["************/26", "*************/26", "*************/26"]
  vpc_name        = var.module_name
  vpc_cidr        = "***********/24"
  internal_cidrs = [
    "**********/16", # Butlins
    "**********/16", # Warner
    "**********/14", # Haven/Shared
    "10.0.0.0/8"     # On-Prem and VPN
  ]
  map_public_ip_on_launch       = true
  manage_default_security_group = false
  manage_default_route_table    = false
  manage_default_network_acl    = false

  domains = {

    "blg-local" = {
      fqdn        = "blg.local"
      nameservers = ["**************", "*************"]
    }

    "bourne-leisure" = {
      fqdn        = "bourne-leisure.co.uk"
      nameservers = ["*************"]
    }
  }
}

