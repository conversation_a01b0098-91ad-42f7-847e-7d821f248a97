data "aws_iam_policy_document" "devops_assume_policy" {
  statement {
    actions = ["sts:AssumeRole"]

    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::${var.aws_account_ids["iamldap"]}:root"]
    }
  }
}

# This is the administrator role used by engineers
resource "aws_iam_role" "devops" {
  name = "aw-blg-crushftp-prod-devops"

  assume_role_policy  = data.aws_iam_policy_document.devops_assume_policy.json
  managed_policy_arns = [data.aws_iam_policy.administrator.arn]

  tags = local.resource_tags
}
