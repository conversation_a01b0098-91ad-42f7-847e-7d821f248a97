data "terraform_remote_state" "platform_kubernetes" {
  backend = "s3"

  config = {
    bucket  = var.terraform_state_bucket
    key     = "tooling/platform-kubernetes"
    profile = var.terraform_state_profile
    region  = var.terraform_state_region
  }
}

data "terraform_remote_state" "platform_k8s" {
  backend = "s3"

  config = {
    bucket  = var.terraform_state_bucket
    key     = "tooling/platform-k8s"
    profile = var.terraform_state_profile
    region  = var.terraform_state_region
  }
}


data "aws_iam_policy_document" "platform_admin_assume_policy" {
  statement {
    actions = ["sts:AssumeRole"]

    principals {
      type = "AWS"
      identifiers = [
        data.terraform_remote_state.platform_k8s.outputs.admin_runner_role_arn
      ]
    }
  }
}

resource "aws_iam_role" "platform_admin" {
  name = "platform-admin"

  assume_role_policy  = data.aws_iam_policy_document.platform_admin_assume_policy.json
  managed_policy_arns = [data.aws_iam_policy.administrator.arn]

  tags = local.resource_tags
}
