resource "aws_acm_certificate" "crushftp_haven_cert" {
  domain_name       = "crushftp.prod.haven-leisure.com"
  validation_method = "DNS"

  tags = local.resource_tags
}

resource "aws_acm_certificate_validation" "crushftp_haven_cert_validation" {
  certificate_arn         = aws_acm_certificate.crushftp_haven_cert.arn
  validation_record_fqdns = values(aws_route53_record.prod_haven_leisure_cert_validation)[*].fqdn
}

module "nlb_crushftp" {
  source  = "terraform-aws-modules/alb/aws"
  version = "9.11.2"

  name               = local.resource_name
  load_balancer_type = "network"
  vpc_id             = data.aws_vpc.platform_network.id
  subnets            = data.aws_subnets.platform_network_private.ids
  internal           = true

  enable_cross_zone_load_balancing = true

  security_group_ingress_rules = {
    rdp = {
      from_port   = 3389
      to_port     = 3389
      ip_protocol = "tcp"
      cidr_ipv4   = "**********/14"
    }
    winrm = {
      from_port   = 5985
      to_port     = 5985
      ip_protocol = "tcp"
      cidr_ipv4   = "**********/14"
    }

    crushftp = {
      from_port   = 443
      to_port     = 443
      ip_protocol = "tcp"
      cidr_ipv4   = "**********/14"
    }

  }

  security_group_egress_rules = {
    all = {
      ip_protocol = "-1"
      cidr_ipv4   = data.aws_vpc.platform_network.cidr_block
    }
  }


  listeners = {
    rdp = {
      port     = 3389
      protocol = "TCP"

      forward = {
        target_group_key = "rdp"
      }
    }

    winrm = {
      port     = 5985
      protocol = "TCP"

      forward = {
        target_group_key = "winrm"
      }
    }

    crushftp = {
      port            = 443
      protocol        = "TLS"
      certificate_arn = aws_acm_certificate.crushftp_haven_cert.arn

      forward = {
        target_group_key = "crushftp"
      }
    }
  }

  target_groups = {
    rdp = {
      protocol             = "TCP"
      port                 = 3389
      target_type          = "instance"
      deregistration_delay = 5
      create_attachment    = false
    }

    winrm = {
      protocol             = "TCP"
      port                 = 5985
      target_type          = "instance"
      deregistration_delay = 5
      create_attachment    = false
    }

    crushftp = {
      protocol             = "TLS"
      port                 = 443
      target_type          = "instance"
      deregistration_delay = 5
      create_attachment    = false
    }
  }

  tags = local.resource_tags
}