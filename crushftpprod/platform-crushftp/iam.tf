resource "aws_iam_policy" "ssm_session_manager" {
  name        = "${local.resource_name}-ssm-session-manager"
  description = "Provides required permissions for SSM Session Manager"
  path        = "/"
  policy      = data.aws_iam_policy_document.ssm_session_manager.json
  tags        = local.resource_tags
}


data "aws_iam_policy_document" "ssm_session_manager" {
  statement {
    actions = [
      "ssm:UpdateInstanceInformation",
      "ssmmessages:CreateControlChannel",
      "ssmmessages:CreateDataChannel",
      "ssmmessages:OpenControlChannel",
      "ssmmessages:OpenDataChannel",
      "ssm:GetParameter",
      "ssm:StartAutomationExecution",
      "ssm:SendCommand",
      "ecr:GetAuthorizationToken"
    ]

    resources = [
      "*"
    ]
  }
}

data "aws_iam_policy_document" "crushftp_snapshots_assume_role" {
  statement {
    effect = "Allow"
    principals {
      type        = "Service"
      identifiers = ["dlm.amazonaws.com"]
    }
    actions = ["sts:AssumeRole"]
  }
}


resource "aws_iam_role" "crushftp_snapshots" {
  name               = "crushftp-prod-snapshots"
  assume_role_policy = data.aws_iam_policy_document.crushftp_snapshots_assume_role.json
  tags               = local.resource_tags
}

resource "aws_iam_role_policy" "crushftp_snapshots_policy" {
  name = "crushftp-prod-snapshots"
  role = aws_iam_role.crushftp_snapshots.id
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "ec2:CreateSnapshot",
          "ec2:CreateSnapshots",
          "ec2:DeleteSnapshot",
          "ec2:DescribeSnapshots",
          "ec2:DescribeVolumes",
          "ec2:DescribeInstances"
        ]
        Effect   = "Allow"
        Resource = "*"
      },
      {
        Action   = ["ec2:CreateTags"]
        Effect   = "Allow"
        Resource = ["arn:aws:ec2:*::snapshot/*"]
      },
    ]
  })
}

resource "aws_iam_policy" "crushftp_s3_access" {
  name        = "CrushFtpProdS3Access"
  description = "S3 access for CrushFTP"
  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Action = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:DeleteObject",
          "s3:AbortMultipartUpload",
          "s3:GetBucketLocation",
          "s3:ListBucket",
          "s3:ListBucketMultipartUploads",
          "s3:ListMultipartUploadParts"
        ],
        Resource = [
          "arn:aws:s3:::haven-prod-ownership-owners-document-legacy-statements",
          "arn:aws:s3:::haven-prod-ownership-owners-document-legacy-statements/*"
        ]
      }
    ]
  })
}
