module "security_group_crushftp_egress" {
  source  = "terraform-aws-modules/security-group/aws"
  version = "5.2.0"

  name            = "${local.resource_name}-egress"
  description     = "Security group for crushftp-prod"
  vpc_id          = data.aws_vpc.platform_network.id
  use_name_prefix = false

  egress_rules = ["all-all"]

  tags = local.resource_tags
}

module "security_group_crushftp_ingress" {
  source  = "terraform-aws-modules/security-group/aws"
  version = "5.2.0"

  name            = "${local.resource_name}-ingress"
  description     = "Ingress permits to crushftp-prod"
  vpc_id          = data.aws_vpc.platform_network.id
  use_name_prefix = false

  ingress_rules       = ["rdp-tcp", "winrm-http-tcp", "https-443-tcp"]
  ingress_cidr_blocks = ["**********/14"]

  tags = local.resource_tags
}