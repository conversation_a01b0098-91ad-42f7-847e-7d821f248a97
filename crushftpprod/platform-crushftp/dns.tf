provider "aws" {
  alias   = "prod_account"
  region  = "eu-west-1"
  profile = "bourne-prod"
}

resource "aws_route53_zone_association" "prod_haven_leisure_crushptp_prod" {
  vpc_id  = data.aws_vpc.platform_network.id # crushftp-prod
  zone_id = "Z087260813NK12A69EC1R"          # prod.haven-leisure.com
}


resource "aws_route53_record" "prod_haven_leisure_cert_validation" {
  provider = aws.prod_account
  for_each = {
    for dvo in aws_acm_certificate.crushftp_haven_cert.domain_validation_options : dvo.domain_name => {
      name   = dvo.resource_record_name
      record = dvo.resource_record_value
      type   = dvo.resource_record_type
    }
  }

  allow_overwrite = true
  name            = each.value.name
  records         = [each.value.record]
  ttl             = 300
  type            = each.value.type
  zone_id         = "Z04108562VVHSVEXV60ZU"
}