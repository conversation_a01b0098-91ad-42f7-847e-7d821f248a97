data "aws_ebs_volume" "root" {
  filter {
    name   = "tag:Name"
    values = ["crushftp-prod-root-volume"]
  }
  most_recent = true
}

resource "aws_dlm_lifecycle_policy" "crushftp_snapshots" {
  description        = "crushftp-prod-snapshots"
  execution_role_arn = aws_iam_role.crushftp_snapshots.arn
  state              = "ENABLED"

  policy_details {
    resource_types = ["VOLUME"]
    schedule {
      name = "1 week of daily snapshots"
      create_rule {
        interval      = 24
        interval_unit = "HOURS"
        times         = ["01:00"]
      }
      retain_rule {
        count = 7
      }
      copy_tags = true
    }
    target_tags = {
      Name = data.aws_ebs_volume.root.tags["Name"]
    }
  }

  tags = local.resource_tags
}