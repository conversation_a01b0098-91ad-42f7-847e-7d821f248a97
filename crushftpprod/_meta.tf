locals {
  team_name  = "platform"
  tribe_name = "platform"

  resource_name = "crushftp-prod"
  resource_tags = {
    "blg:access"         = "corp"
    "blg:account"        = "prod"
    "blg:brand"          = "haven"
    "blg:classification" = "private"
    "blg:environment"    = var.environment_name
    "blg:module"         = var.module_name
    "blg:provisionedby"  = "terraform"
    "blg:risk"           = "high"
    "blg:serviceowner"   = "platform"
    "blg:service"        = try(regex("[^/]+$", "${path.cwd}"), "no-path")
  }

  meta = {
    environment_name = var.environment_name
    module_name      = var.module_name
    team_name        = "platform"
    tribe_name       = "platform"
  }
}