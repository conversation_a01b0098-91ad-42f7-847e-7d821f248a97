# associate the platform-network vpc with the bll-group.co.uk private hosted zone
resource "aws_route53_zone_association" "platform_network_bll_group_co_uk" {
  vpc_id  = data.aws_vpc.platform_network.id # platform-network vpc
  zone_id = "Z066201934N02C6KZ2M6O"          # bll-group.co.uk in network/base-infra
}

# associate the platform-network vpc with the havdev.bll-group.co.uk private hosted zone
resource "aws_route53_zone_association" "platform_network_havdev_bll_group_co_uk" {
  vpc_id  = data.aws_vpc.platform_network.id # platform-network vpc
  zone_id = "Z08540173GSZ4EB46RB6Z"          # havdev.bll-group.co.uk in havdevserv/base-infra
}

# associate the platform-network vpc with the plot.havdev.bll-group.co.uk private hosted zone
resource "aws_route53_zone_association" "platform_network_plot_havdev_bll_group_co_uk" {
  vpc_id  = data.aws_vpc.platform_network.id # platform-network vpc
  zone_id = "Z02494274UALUSUE6NA5"           # plot.havdev.bll-group.co.uk in havdevserv/plot
}

# associate the platform-network vpc with the havprod.bll-group.co.uk private hosted zone
resource "aws_route53_zone_association" "platform_network_havprod_bll_group_co_uk" {
  vpc_id  = data.aws_vpc.platform_network.id # platform-network vpc
  zone_id = "Z0557202169VQ0P3CJQR9"          # havprod.bll-group.co.uk in havprodserv/base-infra
}

# associate the platform-network vpc with the plot.havprod.bll-group.co.uk private hosted zone
resource "aws_route53_zone_association" "platform_network_plot_havprod_bll_group_co_uk" {
  vpc_id  = data.aws_vpc.platform_network.id # platform-network vpc
  zone_id = "Z0516843152975HU6KLH6"          # plot.havprod.bll-group.co.uk in havprodserv/plot
}

# associate the platform-network vpc with the haven-dev.com private hosted zone
resource "aws_route53_zone_association" "platform_network_haven_dev_com" {
  vpc_id  = data.aws_vpc.platform_network.id # platform-network vpc
  zone_id = "Z02170472C2AIHE9LDPS3"          # haven-dev.com in platform-terraform/dev/platform-dns
}

# associate the platform-network vpc with the haven-leisure.com private hosted zone
resource "aws_route53_zone_association" "platform_network_haven_leisure_com" {
  vpc_id  = data.aws_vpc.platform_network.id # platform-network vpc
  zone_id = "Z00085503BSK4LX1U45LV"          # haven-leisure.com in network/base-infra
}

# associate the platform-network vpc with the dev.haven-leisure.com private hosted zone
resource "aws_route53_zone_association" "platform_network_dev_haven_leisure_com" {
  vpc_id  = data.aws_vpc.platform_network.id # platform-network vpc
  zone_id = "Z0641457244UIP2SZ79N2"          # dev.haven-leisure.com in platform-manifests/dev/platform-dns
}

# associate the platform-network vpc with the prod.haven-leisure.com private hosted zone
resource "aws_route53_zone_association" "platform_network_prod_haven_leisure_com" {
  vpc_id  = data.aws_vpc.platform_network.id # platform-network vpc
  zone_id = "Z087260813NK12A69EC1R"          # prod.haven-leisure.com in platform-manifests/prod/platform-dns
}

# associate the platform-network vpc with the tooling.haven-leisure.com private hosted zone
resource "aws_route53_zone_association" "platform_network_tooling_haven_leisure_com" {
  vpc_id  = data.aws_vpc.platform_network.id # platform-network vpc
  zone_id = "Z07973103AVTRHEODW1DL"          # tooling.haven-leisure.com in platform-manifests/tooling/platform-dns
}

# associate the platform-network vpc with the haven-private.com private hosted zone
resource "aws_route53_zone_association" "platform_network_haven_private_com" {
  vpc_id  = data.aws_vpc.platform_network.id # platform-network vpc
  zone_id = "Z00027982RWXMFJCV8GTF"          # haven-private.com in network/base-infra
}

# associate the platform-network vpc with the dev.haven-private.com private hosted zone
resource "aws_route53_zone_association" "platform_network_dev_haven_private_com" {
  vpc_id  = data.aws_vpc.platform_network.id # platform-network vpc
  zone_id = "Z0277649GYFUG7X3EVIH"           # dev.haven-private.com in platform-manifests/dev/platform-dns
}

# associate the platform-network vpc with the prod.haven-private.com private hosted zone
resource "aws_route53_zone_association" "platform_network_prod_haven_private_com" {
  vpc_id  = data.aws_vpc.platform_network.id # platform-network vpc
  zone_id = "Z009723837IW9V7X04QA6"          # prod.haven-private.com in platform-manifests/prod/platform-dns
}

# associate the platform-network vpc with the tooling.haven-private.com private hosted zone
resource "aws_route53_zone_association" "platform_network_tooling_haven_private_com" {
  vpc_id  = data.aws_vpc.platform_network.id # platform-network vpc
  zone_id = "Z0379335R5T4Y9LLWQJ0"           # tooling.haven-private.com in platform-manifests/tooling/platform-dns
}
