# RDS Custom requires it's own KMS key - and cannot use and AWS managed one

resource "aws_kms_key" "rds_custom_kms_key" {
  description              = "KMS key for RDS Custom SQL Server"
  key_usage                = "ENCRYPT_DECRYPT"
  customer_master_key_spec = "SYMMETRIC_DEFAULT"
  enable_key_rotation      = true

  tags = local.default_tags
}

resource "aws_kms_alias" "rds_custom_kms_alias" {
  name          = "alias/custom-rds-sqlserver"
  target_key_id = aws_kms_key.rds_custom_kms_key.key_id
}

resource "aws_kms_key_policy" "rds_custom_kms_policy" {
  key_id = aws_kms_key.rds_custom_kms_key.id
  policy = jsonencode({
    Version = "2012-10-17",
    Statement : [
      {
        Sid : "Allow key management",
        Effect : "Allow",
        Principal : {
          AWS : "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
        },
        Action : "kms:*",
        Resource : "*"
      },
      {
        Sid : "Allow our instances to access the key",
        Effect = "Allow",
        Principal = {
          AWS = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/AWSRDSCustomSQLServerInstanceRole"
        },
        Action = [
          "kms:Decrypt",
          "kms:GenerateDataKey"
        ],
        Resource = "*"
      }
    ]
  })
}
