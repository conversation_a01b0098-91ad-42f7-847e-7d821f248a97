resource "aws_iam_role" "rds_custom_sqlserver_role" {
  name = "AWSRDSCustomSQLServerInstanceRole"

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement : [
      {
        Effect = "Allow",
        Principal = {
          Service = "ec2.amazonaws.com"
        },
        Action = "sts:AssumeRole"
      }
    ]
  })

  tags = local.default_tags
}

resource "aws_iam_role_policy_attachment" "rds_custom_service_role_policy" {
  role       = aws_iam_role.rds_custom_sqlserver_role.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonRDSCustomInstanceProfileRolePolicy"
}

resource "aws_iam_role_policy_attachment" "rds_custom_service_role_policy_ssm" {
  role       = aws_iam_role.rds_custom_sqlserver_role.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"
}


resource "aws_iam_instance_profile" "rds_custom_sqlserver_profile" {
  name = "AWSRDSCustomSQLServerInstanceProfileNew"
  role = aws_iam_role.rds_custom_sqlserver_role.name

  tags = local.default_tags
}
