resource "tls_private_key" "windows_key" {
  algorithm = "RSA"
  rsa_bits  = 4096
}

resource "aws_key_pair" "windows_key_pair" {
  key_name   = "windows-key"
  public_key = tls_private_key.windows_key.public_key_openssh
}

resource "aws_secretsmanager_secret" "windows_key_secret" {
  name = "haven/custom-rds-manual-builder/windows_key"
}

resource "aws_secretsmanager_secret_version" "windows_key_secret_version" {
  secret_id     = aws_secretsmanager_secret.windows_key_secret.id
  secret_string = tls_private_key.windows_key.private_key_pem
}

data "aws_ami" "windows_server_2019" {
  most_recent = true
  owners      = ["amazon"]

  filter {
    name   = "name"
    values = ["Windows_Server-2019-English-Full-Base-*"]
  }

  filter {
    name   = "virtualization-type"
    values = ["hvm"]

  }
}

resource "aws_iam_policy" "windows_ssm_gui_policy" {
  name        = "WindowsSSMGuiPolicy"
  description = "Allow SSM GUI connection for Fleet Manager"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [{
      Effect = "Allow"
      Action = [
        "ssm-guiconnect:GetConnection",
        "ssm-guiconnect:StartConnection",
      ]
      Resource = "*"
    }]
  })

  tags = local.default_tags
}

resource "aws_iam_policy" "s3_access" {
  name        = "s3-access-policy"
  description = "Allow instance to read/write to haven-prod-cloud-mssql-installation-media"
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:ListBucket"
        ]
        Resource = [
          "arn:aws:s3:::haven-prod-cloud-mssql-installation-media",
          "arn:aws:s3:::haven-prod-cloud-mssql-installation-media/*"
        ]
      }
    ]
  })
}

module "ec2_instance" {
  source  = "terraform-aws-modules/ec2-instance/aws"
  version = "5.7.1"

  create        = false
  name          = "custom-rds-manual-builder"
  ami           = data.aws_ami.windows_server_2019.id
  instance_type = "m5.xlarge"

  subnet_id = element(values(data.aws_subnet.platform_network_intra), 0).id

  key_name = aws_key_pair.windows_key_pair.key_name

  enable_volume_tags = true
  monitoring         = true

  root_block_device = [{
    volume_size = 100
  }]

  create_iam_instance_profile = true
  iam_role_policies = {
    ssm_managed_core = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"
    s3_access        = aws_iam_policy.s3_access.arn
    windows_ssm_gui  = aws_iam_policy.windows_ssm_gui_policy.arn
  }

  vpc_security_group_ids = [module.security_group_internal_rdp.security_group_id]

  metadata_options = {
    http_endpoint               = "enabled"
    http_tokens                 = "required"
    http_put_response_hop_limit = 1
    instance_metadata_tags      = "enabled"
  }

  instance_tags = merge(local.default_tags, {
    Name = "custom-rds-manual-builder"
  })
  tags = local.default_tags
}

