# Define variables
$bucketName = "haven-prod-cloud-mssql-installation-media"
$mediaFolder = "C:\Media"

# following are requirements of BYOM: https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/custom-sqlserver.byom.html#custom-sqlserver.byom.requirements
$sqlInstallerExe = "SQLServer2022-DEV-x64-ENU.exe"
$sqlInstallerBox = "SQLServer2022-DEV-x64-ENU.box"
$sqlManagementStudioExe = "SSMS-Setup.exe"
$CUVersion = "CU17" # this needs to be extracted and ran as a parameter when we build for proper
$SQLSvcAccount = "NT AUTHORITY\NETWORK SERVICE"

$CUPath = "SQLServer2022CU/"
$SAPassword = "REDACTED" # we MUST set this but it will be changed as instances are launched as we made Administrator the sysadmin

$SQLSysAdminAccounts = "Administrator"

# Check if the SqlServer module is installed
if (-not (Get-Module -ListAvailable -Name SqlServer)) {
    Write-Host "SqlServer module not found. Installing..."
    
    # Ensure NuGet is installed
    if (-not (Get-PackageProvider -ListAvailable -Name NuGet)) {
        Write-Host "NuGet package provider not found. Installing..."
        Install-PackageProvider -Name NuGet -Force
    }

    # Ensure PowerShell Gallery is trusted
    if ((Get-PSRepository -Name PSGallery).InstallationPolicy -ne "Trusted") {
        Write-Host "Setting PSGallery as trusted..."
        Set-PSRepository -Name PSGallery -InstallationPolicy Trusted
    }

    # Install the SqlServer module
    Install-Module -Name SqlServer -Scope AllUsers -Force
} else {
    Write-Host "SqlServer module is already installed. Skipping installation."
}

# Import the module to ensure it's available in the session
Import-Module SqlServer -ErrorAction SilentlyContinue

# Confirm that Invoke-Sqlcmd is available
if (Get-Command -Module SqlServer -Name Invoke-Sqlcmd) {
    Write-Host "Invoke-Sqlcmd is available."
} else {
    Write-Host "Invoke-Sqlcmd not found. Something went wrong with the installation."
}

# Ensure Media folder exists
if (-Not (Test-Path $mediaFolder)) {
    New-Item -Path $mediaFolder -ItemType Directory | Out-Null
} else {
    Write-Host "Folder $mediaFolder already exists"
}

# Download files only if they do not exist
if (-Not (Test-Path "${mediaFolder}\${sqlInstallerExe}")) {
    Read-S3Object -BucketName $bucketName -Key "${sqlInstallerExe}" -File "${mediaFolder}\${sqlInstallerExe}"
} else {
    Write-Host "File ${sqlInstallerExe} already exists"
}

if (-Not (Test-Path "${mediaFolder}\${sqlInstallerBox}")) {
    Read-S3Object -BucketName $bucketName -Key "${sqlInstallerBox}" -File "${mediaFolder}\${sqlInstallerBox}"
    Write-Host "Downloaded file ${sqlInstallerBox}"
} else {
    Write-Host "File ${sqlInstallerBox} already exists"
}

if (-Not (Test-Path "${mediaFolder}\${sqlManagementStudioExe}")) {
    Read-S3Object -BucketName $bucketName -Key "${sqlManagementStudioExe}" -File "${mediaFolder}\${sqlManagementStudioExe}"
    Write-Host "Downloaded file ${sqlManagementStudioExe}"
} else {
    Write-Host "File ${sqlManagementStudioExe} already exists"
}

if (-Not (Test-Path "${mediaFolder}\${CUPath}$CUVersion.exe")) {
    Read-S3Object -BucketName $bucketName -Key "${CUPath}$CUVersion.exe" -File "${mediaFolder}\${CUPath}$CUVersion.exe"
    Write-Host "Downloaded file ${CUPath}${CUVersion}.exe"
} else {
    Write-Host "File ${CUPath}${CUVersion}.exe already exists"
}

# make a folder to extract the box file
$boxFolder = "${mediaFolder}\Installer"


# Make a folder to extract the box file
$boxFolder = "${mediaFolder}\Installer"
$configFilePath = "${boxFolder}\ConfigurationFile.ini"
$setupExePath = "${boxFolder}\setup.exe"

if (-Not (Test-Path $boxFolder)) {
    Write-Host "Creating folder $boxFolder"
    New-Item -Path $boxFolder -ItemType Directory | Out-Null
    Write-Host "Extracting box file to $boxFolder"
    Start-Process -FilePath "${mediaFolder}\${sqlInstallerExe}" -ArgumentList "/Q /X:${boxFolder}" -Wait
    Write-Host "Extracted box file to $boxFolder"
} else {
    Write-Host "Folder $boxFolder already exists"
}

# See https://learn.microsoft.com/en-us/sql/database-engine/install-windows/install-sql-server-from-the-command-prompt?view=sql-server-ver16 for options
# Create the configuration file for unattended installation
# These settings are from https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/custom-cev-sqlserver.preparing.html
$configContent = @"
; SQL Server 2022 Configuration File
[OPTIONS]
ACTION="Install"
FEATURES=SQLEngine
INSTANCENAME="MSSQLSERVER"
SQLSVCACCOUNT="$SQLSvcAccount"
SQLSYSADMINACCOUNTS="$SQLSysAdminAccounts"
IACCEPTSQLSERVERLICENSETERMS="True"
QUIET="True"
UpdateEnabled="False"
SQLSVCSTARTUPTYPE="Manual"
SECURITYMODE="SQL"
SAPWD="$SAPassword"
"@

# Write the configuration content to the file
$configContent | Out-File -FilePath $configFilePath -Encoding ASCII

$logFileOut = "C:\Media\sql_install_log_out.txt"
$logFileError = "C:\Media\sql_install_log_error.txt"

# Check if SQL Server is already installed before running setup
$service = Get-Service -Name "MSSQLSERVER" -ErrorAction SilentlyContinue

if ($service) {
    Write-Host "SQL Server is already installed. Skipping installation."
} else {
    # Execute SQL Server Setup
    Write-Host "Starting SQL Server unattended installation..."
    Start-Process -FilePath $setupExePath `
        -ArgumentList "/ConfigurationFile=$configFilePath" `
        -NoNewWindow -Wait
    Write-Host "SQL Server installation process initiated."
}

Write-Host "Starting SQL Server service..."
Start-Service -Name MSSQLSERVER

$SQLQuery = @"
USE master;
EXEC master..sp_addsrvrolemember @loginame = N'NT AUTHORITY\SYSTEM', @rolename = N'sysadmin';
"@

Write-Host "Adding NT AUTHORITY\SYSTEM to sysadmin role..."
Invoke-Sqlcmd -Query $SQLQuery -ServerInstance "localhost" -TrustServerCertificate

Write-Host "Checking current SQL Server version..."
Invoke-Sqlcmd -Query "SELECT @@VERSION" -ServerInstance "localhost" -TrustServerCertificate


Write-Host "Starting SQL Server patch installation..."
Start-Process -FilePath "${mediaFolder}\${CUPath}${CUVersion}.exe" `
    -ArgumentList "/Q /Action=Patch /IAcceptSQLServerLicenseTerms /Quiet /AllInstances" `
    -Wait
Write-Host "SQL Server patch installation process initiated."

Write-Host "Checking current SQL Server version..."
Invoke-Sqlcmd -Query "SELECT @@VERSION" -ServerInstance "localhost" -TrustServerCertificate

# Open TCP port 1433 for SQL Server
New-NetFirewallRule -DisplayName "Allow SQL TCP 1433" -Direction Inbound -Protocol TCP -LocalPort 1433 -Action Allow

# Open UDP port 1434 for AWS Systems Manager (SSM)
New-NetFirewallRule -DisplayName "Allow SQL UDP 1434" -Direction Inbound -Protocol UDP -LocalPort 1434 -Action Allow

# Remove media folder
Remove-Item -Path "C:\Media" -Recurse -Force

# Write-Host "Running Sysprep..."
& C:\ProgramData\Amazon\EC2-Windows\Launch\Scripts\InitializeInstance.ps1 -Schedule
& C:\ProgramData\Amazon\EC2-Windows\Launch\Scripts\SysprepInstance.ps1