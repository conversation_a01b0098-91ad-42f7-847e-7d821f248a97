resource "aws_iam_user" "replication_agent_user" {
  name = "replication_user"

}


resource "aws_iam_access_key" "replication_user_access_key" {
  user = aws_iam_user.replication_agent_user.name
}

resource "aws_secretsmanager_secret" "replication_user_secret" {
  name = "haven/application-migration-service/replication-user-secret"

  tags = local.default_tags
}

resource "aws_secretsmanager_secret_version" "external_user_secret_version" {
  secret_id = aws_secretsmanager_secret.replication_user_secret.id
  secret_string = jsonencode({
    "AccessKeyId"     = aws_iam_access_key.replication_user_access_key.id,
    "SecretAccessKey" = aws_iam_access_key.replication_user_access_key.secret
  })
}

resource "aws_iam_user_policy_attachment" "attach_aws_migration_policy" {
  user       = aws_iam_user.replication_agent_user.name
  policy_arn = "arn:aws:iam::aws:policy/AWSApplicationMigrationAgentInstallationPolicy"
}
