resource "aws_kinesis_firehose_delivery_stream" "filter_vpc_flow_logs" {
  name        = "filter-vpc-flow-logs"
  destination = "extended_s3"

  server_side_encryption {
    enabled = true
  }
  extended_s3_configuration {
    role_arn            = aws_iam_role.filter_vpc_flow_logs.arn
    bucket_arn          = module.bucket_network_logs_dc_migration.bucket_arn
    compression_format  = "GZIP"
    error_output_prefix = "public-traffic-logs-errors"
    prefix              = "public-traffic-logs-"
    processing_configuration {
      enabled = "true"

      processors {
        type = "Lambda"

        parameters {
          parameter_name  = "LambdaArn"
          parameter_value = "${aws_lambda_function.filter_vpc_flow_logs.arn}:$LATEST"
        }
        parameters {
          parameter_name  = "BufferSizeInMBs"
          parameter_value = "3"
        }
      }
    }
  }
  tags = local.default_tags
}

data "aws_iam_policy_document" "filter_vpc_flow_logs" {
  statement {
    effect = "Allow"

    principals {
      type        = "Service"
      identifiers = ["firehose.amazonaws.com"]
    }

    actions = ["sts:AssumeRole"]
  }

}

resource "aws_iam_role" "filter_vpc_flow_logs" {
  name               = "filter-vpc-flow-logs"
  assume_role_policy = data.aws_iam_policy_document.filter_vpc_flow_logs.json
  inline_policy {
    name = "access-s3-lambda"

    policy = jsonencode({
      Version = "2012-10-17"
      Statement = [
        {
          Action = [
            "s3:AbortMultipartUpload",
            "s3:GetBucketLocation",
            "s3:GetObject",
            "s3:ListBucket",
            "s3:ListBucketMultipartUploads",
            "s3:PutObject"
          ]
          Effect   = "Allow"
          Resource = [module.bucket_network_logs_dc_migration.bucket_arn, "${module.bucket_network_logs_dc_migration.bucket_arn}/*"]
        },
        {
          Action = [
            "lambda:InvokeFunction",
            "lambda:GetFunctionConfiguration"
          ]
          Effect   = "Allow"
          Resource = ["${aws_lambda_function.filter_vpc_flow_logs.arn}:*"]
        },
      ]
    })
  }
  tags = local.default_tags
}

data "aws_iam_policy_document" "filter_vpc_flow_logs_lambda" {
  statement {
    effect = "Allow"

    principals {
      type        = "Service"
      identifiers = ["lambda.amazonaws.com"]
    }

    actions = ["sts:AssumeRole"]
  }
}

resource "aws_iam_role" "filter_vpc_flow_logs_lambda" {
  name               = "filter-vpc-flow-logs-lambda"
  assume_role_policy = data.aws_iam_policy_document.filter_vpc_flow_logs_lambda.json
  tags               = local.default_tags
}

data "archive_file" "filter_vpc_flow_logs" {
  type        = "zip"
  source_file = "index.js"
  output_path = "index.zip"
}

resource "aws_lambda_function" "filter_vpc_flow_logs" {
  filename      = "index.zip"
  function_name = "filter-vpc-flow-logs"
  role          = aws_iam_role.filter_vpc_flow_logs_lambda.arn
  handler       = "index.handler"
  timeout       = 60

  source_code_hash = data.archive_file.filter_vpc_flow_logs.output_base64sha256

  runtime = "nodejs18.x"

  tags = local.default_tags
}

resource "aws_flow_log" "filter_vpc_flow_logs" {
  log_destination      = aws_kinesis_firehose_delivery_stream.filter_vpc_flow_logs.arn
  log_destination_type = "kinesis-data-firehose"
  traffic_type         = "ALL"
  vpc_id               = module.vpc.vpc_id
}