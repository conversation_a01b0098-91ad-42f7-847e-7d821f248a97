function isPublicIP(ip) {
    const parts = ip.split('.').map(Number);

    if (parts.length !== 4) return false;

    const [a, b] = parts;

    const isPrivate = ( 
        a === 10 ||
        (a === 172 && b >= 16 && b <= 31) ||
        (a === 192 && b === 168)
    );

    return !isPrivate
}

exports.handler = async (event) => {
    const output = [];

    for (const record of event.records) {

        const lines = Buffer.from(record.data, 'base64').toString('utf-8').trim().split('\n');
        const filteredLines = [];

        for (const line of lines) {
            const fields = line.trim().split(' ');
            const srcAddr = fields[3]; // source address
            const dstAddr = fields[4]; // destination address

            if (isPublicIP(srcAddr) || isPublicIP(dstAddr)) {
                output.push({
                    recordId: record.recordId,
                    result: 'Ok',
                    data: Buffer.from(line + "\n").toString('base64')
                });
            } else {
                output.push({
                    recordId: record.recordId,
                    result: 'Dropped',
                    data: Buffer.from(line + "\n").toString('base64')
                });
            }
        }
    }
    return { records: output };
    callback(null, {records: output});
};