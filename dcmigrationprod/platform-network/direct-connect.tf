resource "aws_dx_gateway_association_proposal" "platform_network" {
  dx_gateway_id               = var.network_account_dx_gateway_id
  dx_gateway_owner_account_id = var.network_account_id
  associated_gateway_id       = module.vpc.vgw_id
}

resource "aws_vpn_gateway_route_propagation" "private" {
  route_table_id = module.vpc.private_route_table_ids[0]
  vpn_gateway_id = module.vpc.vgw_id
}

resource "aws_vpn_gateway_route_propagation" "database" {
  route_table_id = module.vpc.database_route_table_ids[0]
  vpn_gateway_id = module.vpc.vgw_id
}
