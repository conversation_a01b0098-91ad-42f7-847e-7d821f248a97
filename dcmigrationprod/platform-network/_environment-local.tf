/*
 * Module specific locals
 *
 */

locals {
  azs              = ["${var.aws_region}a", "${var.aws_region}b", "${var.aws_region}c"]
  public_subnets   = ["***********/26", "172.29.48.64/26", "*************/26"]
  private_subnets  = ["***********/26", "************/26", "*************/26"]
  intra_subnets    = ["***********/24", "***********/24", "***********/24"]
  database_subnets = ["***********/25", "*************/25", "***********/25"]
  vpc_name         = var.module_name
  vpc_cidr         = "***********/21"
  internal_cidrs = [
    "**********/16", # Butlins
    "**********/16", # Warner
    "**********/14", # Haven/Shared
    "10.0.0.0/8"     # On-Prem and VPN
  ]
  map_public_ip_on_launch       = true
  manage_default_security_group = false
  manage_default_route_table    = false
  manage_default_network_acl    = false

  domains = {
    "bourne-leisure" = {
      fqdn        = "bourne-leisure.co.uk"
      nameservers = ["*************"]
    }
  }
}


/*
 * Commonly used datasources.
 *
 */

data "aws_ec2_transit_gateway" "network" {
  id = "tgw-03fd0d3acdb4ba683" # Network account transit gateway
}
