
resource "aws_ec2_transit_gateway_vpc_attachment" "network" {
  subnet_ids         = module.vpc.intra_subnets
  transit_gateway_id = data.aws_ec2_transit_gateway.network.id
  vpc_id             = module.vpc.vpc_id

  tags = {
    "Name" = "network-${var.environment_name}-${var.module_name}-tgw"
  }
}

# private subnet to network account vpc
resource "aws_route" "tgw_private_network_vpc" {
  route_table_id         = module.vpc.intra_route_table_ids.0
  destination_cidr_block = "172.30.24.0/21"
  transit_gateway_id     = data.aws_ec2_transit_gateway.network.id
}

# private subnet to havdevserv account vpc1 vpc (base-infra)
resource "aws_route" "tgw_private_havdevserv_vpc1" {
  route_table_id         = module.vpc.intra_route_table_ids.0
  destination_cidr_block = "172.31.224.0/20"
  transit_gateway_id     = data.aws_ec2_transit_gateway.network.id
}

# private subnet to havdevserv account k8s-infra vpc (k8s-infra)
resource "aws_route" "tgw_private_havdevserv_k8s_infra" {
  route_table_id         = module.vpc.intra_route_table_ids.0
  destination_cidr_block = "172.31.240.0/20"
  transit_gateway_id     = data.aws_ec2_transit_gateway.network.id
}

# private subnet to havprodserv account vpc1 vpc (base-infra)
resource "aws_route" "tgw_private_havprodserv_vpc1" {
  route_table_id         = module.vpc.intra_route_table_ids.0
  destination_cidr_block = "172.31.160.0/20"
  transit_gateway_id     = data.aws_ec2_transit_gateway.network.id
}

# private subnet to havprodserv account k8s-infra vpc (k8s-infra)
resource "aws_route" "tgw_private_havprodserv_k8s_infra" {
  route_table_id         = module.vpc.intra_route_table_ids.0
  destination_cidr_block = "172.31.176.0/20"
  transit_gateway_id     = data.aws_ec2_transit_gateway.network.id
}

# private subnet to haven digital dev account platform-network vpc (platform-network)
resource "aws_route" "tgw_private_havendigital_dev_platform_network" {
  route_table_id         = module.vpc.intra_route_table_ids.0
  destination_cidr_block = "172.25.160.0/19"
  transit_gateway_id     = data.aws_ec2_transit_gateway.network.id
}

# private subnet to haven digital prod account platform-network vpc (platform-network)
resource "aws_route" "tgw_private_havendigital_prod_platform_network" {
  route_table_id         = module.vpc.intra_route_table_ids.0
  destination_cidr_block = "172.25.128.0/19"
  transit_gateway_id     = data.aws_ec2_transit_gateway.network.id
}

# private subnet to haven digital tooling account platform-network vpc (platform-network)
resource "aws_route" "tgw_private_havendigital_tooling_platform_network" {
  route_table_id         = module.vpc.intra_route_table_ids.0
  destination_cidr_block = "172.25.224.0/20"
  transit_gateway_id     = data.aws_ec2_transit_gateway.network.id
}


# database subnet to network account vpc
resource "aws_route" "tgw_db_network_vpc" {
  route_table_id         = module.vpc.database_route_table_ids.0
  destination_cidr_block = "172.30.24.0/21"
  transit_gateway_id     = data.aws_ec2_transit_gateway.network.id
}

# database subnet to havdevserv account vpc1 vpc (base-infra)
resource "aws_route" "tgw_db_havdevserv_vpc1" {
  route_table_id         = module.vpc.database_route_table_ids.0
  destination_cidr_block = "172.31.224.0/20"
  transit_gateway_id     = data.aws_ec2_transit_gateway.network.id
}

# database subnet to havdevserv account k8s-infra vpc (k8s-infra)
resource "aws_route" "tgw_db_havdevserv_k8s_infra" {
  route_table_id         = module.vpc.database_route_table_ids.0
  destination_cidr_block = "172.31.240.0/20"
  transit_gateway_id     = data.aws_ec2_transit_gateway.network.id
}

# database subnet to havprodserv account vpc1 vpc (base-infra)
resource "aws_route" "tgw_db_havprodserv_vpc1" {
  route_table_id         = module.vpc.database_route_table_ids.0
  destination_cidr_block = "172.31.160.0/20"
  transit_gateway_id     = data.aws_ec2_transit_gateway.network.id
}

# database subnet to havprodserv account k8s-infra vpc (k8s-infra)
resource "aws_route" "tgw_db_havprodserv_k8s_infra" {
  route_table_id         = module.vpc.database_route_table_ids.0
  destination_cidr_block = "172.31.176.0/20"
  transit_gateway_id     = data.aws_ec2_transit_gateway.network.id
}

# database subnet to haven digital dev account platform-network vpc (platform-network)
resource "aws_route" "tgw_db_havendigital_dev_platform_network" {
  route_table_id         = module.vpc.database_route_table_ids.0
  destination_cidr_block = "172.25.160.0/19"
  transit_gateway_id     = data.aws_ec2_transit_gateway.network.id
}

# database subnet to haven digital prod account platform-network vpc (platform-network)
resource "aws_route" "tgw_db_havendigital_prod_platform_network" {
  route_table_id         = module.vpc.database_route_table_ids.0
  destination_cidr_block = "172.25.128.0/19"
  transit_gateway_id     = data.aws_ec2_transit_gateway.network.id
}

# database subnet to haven digital tooling account platform-network vpc (platform-network)
resource "aws_route" "tgw_db_havendigital_tooling_platform_network" {
  route_table_id         = module.vpc.database_route_table_ids.0
  destination_cidr_block = "172.25.224.0/20"
  transit_gateway_id     = data.aws_ec2_transit_gateway.network.id
}
