module "security_group_internal_any" {
  source  = "terraform-aws-modules/security-group/aws"
  version = "5.2.0"

  name        = "${var.module_name}-internal-any"
  description = "Security group for any internal communication within the VPC"
  vpc_id      = module.vpc.vpc_id

  ingress_cidr_blocks = [for cidr in local.internal_cidrs : cidr]
  ingress_rules       = ["all-all"]

  egress_cidr_blocks = ["0.0.0.0/0"]
  egress_rules       = ["all-all"]
}

module "security_group_route53_resolver" {
  source  = "terraform-aws-modules/security-group/aws"
  version = "5.2.0"

  name        = "${var.module_name}-internal-dns"
  description = "Security group for route53 resolver within the VPC"
  vpc_id      = module.vpc.vpc_id

  ingress_cidr_blocks = [module.vpc.vpc_cidr_block]
  ingress_rules       = ["dns-tcp", "dns-udp"]

  egress_cidr_blocks = ["0.0.0.0/0"]
  egress_rules       = ["all-all"]
}
