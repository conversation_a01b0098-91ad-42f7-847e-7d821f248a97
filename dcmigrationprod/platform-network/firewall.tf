resource "aws_networkfirewall_firewall" "dc_migration" {
  name                = "dc-migration"
  firewall_policy_arn = aws_networkfirewall_firewall_policy.dc_migration.arn
  vpc_id              = module.vpc.vpc_id
  subnet_mapping {
    subnet_id = module.vpc.public_subnets[0]
  }

  tags = local.default_tags
}

resource "aws_networkfirewall_firewall_policy" "dc_migration" {
  name = "dc-migration"

  firewall_policy {
    stateless_default_actions          = ["aws:forward_to_sfe"]
    stateless_fragment_default_actions = ["aws:forward_to_sfe"]
    stateful_rule_group_reference {
      priority     = 1
      resource_arn = aws_networkfirewall_rule_group.allowed_hosts.arn
    }
    stateful_engine_options {
      rule_order = "STRICT_ORDER"
    }
    stateful_default_actions = ["aws:drop_strict"]
  }

  tags = local.default_tags
}

resource "aws_networkfirewall_rule_group" "allowed_hosts" {
  capacity = 3000
  name     = "allowed-hosts"
  type     = "STATEFUL"
  rule_group {
    rules_source {
      rules_string = templatefile("allowed-hosts.tftpl",
        { # The calculation is to generate an incremental ID for each rule. To update allowed hosts edit allowed-hosts.txt. After applying wait for reconcilliation, you can see the status under VPC -> Firewalls.
          hosts = { for x in flatten(toset(regexall("(.*)", file("allowed-hosts.txt")))) : x => index(flatten(toset(regexall("(.*)", file("allowed-hosts.txt")))), x) * 2 + 1000 }
        }
      )
    }
    stateful_rule_options {
      rule_order = "STRICT_ORDER"
    }
  }
  tags = local.default_tags
}

# Logs
resource "aws_networkfirewall_logging_configuration" "dc_migration" {
  firewall_arn = aws_networkfirewall_firewall.dc_migration.arn
  logging_configuration {
    log_destination_config {
      log_destination = {
        bucketName = module.bucket_network_logs_dc_migration.bucket_name
      }
      log_destination_type = "S3"
      log_type             = "FLOW"
    }
    log_destination_config {
      log_destination = {
        bucketName = module.bucket_network_logs_dc_migration.bucket_name
      }
      log_destination_type = "S3"
      log_type             = "ALERT"
    }
    log_destination_config {
      log_destination = {
        bucketName = module.bucket_network_logs_dc_migration.bucket_name
      }
      log_destination_type = "S3"
      log_type             = "TLS"
    }
  }
}