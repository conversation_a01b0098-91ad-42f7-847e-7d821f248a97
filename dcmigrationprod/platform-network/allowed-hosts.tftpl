%{ for host, priority in hosts ~}
pass http $HOME_NET any -> $EXTERNAL_NET 80 (http.host; dotprefix; content:"${host}"; endswith; msg:"Pass HTTP to ${host}"; sid:${priority}; rev:1;)
pass tls $HOME_NET any -> $EXTERNAL_NET 443 (tls.sni; dotprefix; content:"${host}"; endswith; msg:"Pass TLS to ${host}"; sid:${priority+1}; rev:1;)
%{ endfor ~}

pass tcp $HOME_NET any <> $EXTERNAL_NET 80 (flow:not_established; sid:10001; rev:1;)
pass tcp $HOME_NET any <> $EXTERNAL_NET 443 (flow:not_established; sid:10002; rev:1;)