resource "aws_route53_resolver_endpoint" "out" {
  name      = "${var.module_name}-outbound-resolver-endpoint"
  direction = "OUTBOUND"

  security_group_ids = [module.security_group_route53_resolver.security_group_id]

  dynamic "ip_address" {
    for_each = [module.vpc.intra_subnets[0], module.vpc.intra_subnets[1]]
    content {
      subnet_id = ip_address.value
    }
  }

  tags = local.default_tags
}

resource "aws_route53_resolver_rule" "forward" {
  for_each = local.domains

  domain_name          = each.value.fqdn
  name                 = each.key
  rule_type            = "FORWARD"
  resolver_endpoint_id = aws_route53_resolver_endpoint.out.id

  dynamic "target_ip" {
    for_each = each.value.nameservers

    content {
      ip = target_ip.value
    }
  }

  tags = local.default_tags
}

resource "aws_route53_resolver_rule_association" "forward" {
  for_each = local.domains

  resolver_rule_id = aws_route53_resolver_rule.forward[each.key].id
  vpc_id           = module.vpc.vpc_id
}
