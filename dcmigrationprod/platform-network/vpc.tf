module "vpc" {
  source  = "terraform-aws-modules/vpc/aws"
  version = "5.19.0"

  name = local.vpc_name
  cidr = local.vpc_cidr

  #Added variables to limit the changes to the VPC's
  map_public_ip_on_launch       = local.map_public_ip_on_launch
  manage_default_security_group = local.manage_default_security_group
  manage_default_route_table    = local.manage_default_route_table
  manage_default_network_acl    = local.manage_default_network_acl

  azs              = local.azs
  public_subnets   = local.public_subnets
  private_subnets  = local.private_subnets
  intra_subnets    = local.intra_subnets
  database_subnets = local.database_subnets

  enable_ipv6 = false

  enable_nat_gateway = false
  single_nat_gateway = true

  enable_dns_hostnames = true
  enable_dns_support   = true


  enable_vpn_gateway = true

  enable_flow_log                                 = true
  flow_log_destination_type                       = "s3"
  flow_log_traffic_type                           = "ALL"
  flow_log_cloudwatch_log_group_retention_in_days = 7
  flow_log_destination_arn                        = module.bucket_network_logs_dc_migration.bucket_arn

  tags = local.default_tags
}

module "vpc_endpoints" {
  source  = "terraform-aws-modules/vpc/aws//modules/vpc-endpoints"
  version = "5.19.0"

  vpc_id             = module.vpc.vpc_id
  security_group_ids = [module.security_group_internal_any.security_group_id]

  endpoints = {
    s3-gateway = {
      service         = "s3"
      service_type    = "Gateway"
      route_table_ids = flatten([module.vpc.intra_route_table_ids, module.vpc.private_route_table_ids, module.vpc.public_route_table_ids])
    }
    s3-interface = {
      service             = "s3"
      private_dns_enabled = true
      subnet_ids          = module.vpc.private_subnets
    }
    ec2 = {
      service             = "ec2"
      private_dns_enabled = true
      subnet_ids          = module.vpc.private_subnets
    }
    ec2messages = {
      service             = "ec2messages"
      private_dns_enabled = true
      subnet_ids          = module.vpc.private_subnets
    }
    ecr-dkr = {
      service             = "ecr.dkr"
      private_dns_enabled = true
      subnet_ids          = module.vpc.private_subnets
    }
    ecr-api = {
      service             = "ecr.api"
      private_dns_enabled = true
      subnet_ids          = module.vpc.private_subnets
    }
    mgn = {
      service             = "mgn"
      private_dns_enabled = true
      subnet_ids          = module.vpc.private_subnets
    }
    secrets = {
      service             = "secretsmanager"
      private_dns_enabled = true
      subnet_ids          = module.vpc.private_subnets
    }
    ssm = {
      service             = "ssm"
      private_dns_enabled = true
      subnet_ids          = module.vpc.private_subnets
    }
    ssmmessages = {
      service             = "ssmmessages"
      private_dns_enabled = true
      subnet_ids          = module.vpc.private_subnets
    }
    monitoring = {
      service             = "monitoring"
      private_dns_enabled = true
      subnet_ids          = module.vpc.private_subnets
    }
    logs = {
      service             = "logs"
      private_dns_enabled = true
      subnet_ids          = module.vpc.private_subnets
    }
    events = {
      service             = "events"
      private_dns_enabled = true
      subnet_ids          = module.vpc.private_subnets
    }
    sqs = {
      service             = "sqs"
      private_dns_enabled = true
      subnet_ids          = module.vpc.private_subnets
    }
  }

  tags = local.default_tags
}

resource "aws_eip" "private_nat" {
  domain = "vpc"
  tags = merge(
    { Name = "platform-network" },
    local.default_tags
  )
}

resource "aws_nat_gateway" "private_nat" {
  allocation_id = aws_eip.private_nat.id
  subnet_id     = module.vpc.private_subnets[0]

  tags = merge(
    { Name = "platform-network" },
    local.default_tags
  )
}

# Set default route to the nat gateway in the intra subnets
resource "aws_route" "intra_default_route_nat" {
  route_table_id         = module.vpc.intra_route_table_ids.0
  destination_cidr_block = "0.0.0.0/0"
  nat_gateway_id         = aws_nat_gateway.private_nat.id
}

# Set default route to firewall in private subnets
resource "aws_route" "private_default_route_firewall" {
  route_table_id         = module.vpc.private_route_table_ids.0
  destination_cidr_block = "0.0.0.0/0"
  vpc_endpoint_id        = tolist(aws_networkfirewall_firewall.dc_migration.firewall_status[0].sync_states)[0].attachment[0].endpoint_id
}

# Route table for the public IGW to point back at the firewall for intra subnets
resource "aws_route_table" "igw" {
  vpc_id = module.vpc.vpc_id

  tags = merge(
    { Name = "platform-network-igw" },
    local.default_tags
  )
}

resource "aws_route_table_association" "igw" {
  gateway_id     = module.vpc.igw_id
  route_table_id = aws_route_table.igw.id
}

resource "aws_route" "igw_private_firewall" {
  for_each               = toset(module.vpc.private_subnets_cidr_blocks)
  route_table_id         = aws_route_table.igw.id
  destination_cidr_block = each.value
  vpc_endpoint_id        = tolist(aws_networkfirewall_firewall.dc_migration.firewall_status[0].sync_states)[0].attachment[0].endpoint_id
}