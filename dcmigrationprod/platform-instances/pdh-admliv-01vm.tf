#
# Instance migrated by AWS Application Migration Service
#

# pdh-admliv-01vm
module "ec2_instance_pdh_admliv_01vm" {
  source  = "terraform-aws-modules/ec2-instance/aws"
  version = "5.7.1"

  name          = "PDH-ADMLIV-01VM"
  ami           = "ami-038adb0674fed4615"
  instance_type = "c5.large"
  subnet_id     = "subnet-0f71b023f87bb2705" # platform-network-private-eu-west-1a
  vpc_security_group_ids = [
    module.security_group_admliv.security_group_id
  ]

  enable_volume_tags = false
  root_block_device = [
    {

      volume_type = "gp3"
      volume_size = 60
      encrypted   = true
      tags = merge(local.default_tags, {
        Name : "PDH-ADMLIV-01VM"
        AWSApplicationMigrationServiceManaged : "mgn.amazonaws.com"
        AWSApplicationMigrationServiceSourceServerID : "s-003a657a02009e08a"
      })
    }
  ]

  launch_template = {
    id      = "lt-02664a9c134f7852e"
    version = "7"
  }

  create_iam_instance_profile = true
  iam_role_policies = {
    ssm_managed_core = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"
  }

  metadata_options = {
    http_endpoint               = "enabled"
    http_tokens                 = "required"
    http_put_response_hop_limit = 1
    instance_metadata_tags      = "enabled"
  }

  tags = merge(local.default_tags, {
    Name         = "pdh-admliv-01vm"
    "blg:backup" = "7d2w12m"
  })
}

module "security_group_admliv" {
  source  = "terraform-aws-modules/security-group/aws"
  version = "5.1.2"

  name        = "${var.environment_name}-${var.module_name}-admliv"
  description = "Security group for the ADMLIV access"
  vpc_id      = data.aws_vpc.platform_network.id

  ingress_with_cidr_blocks = [
    {
      from_port   = 3389
      to_port     = 3389
      protocol    = "tcp"
      description = "Allow BeyondTrust access"
      cidr_blocks = local.beyond_trust_cidr
    },
  ]

  egress_rules = ["all-all"]
}
