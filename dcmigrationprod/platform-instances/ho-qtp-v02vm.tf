# instance migrated from data center to AWS
#
# 1. export from vmware as ova
# <carried on in vCenter>
#
# 2. import ho-qtp-vm02vm
# aws ec2 import-image --description "ho-qtp-vm02vm" --platform Windows --license-type BYOL --disk-containers "Format=OVA,UserBucket={S3Bucket=haven-prod-vm-migration,S3Key=ho-qtp-t02vm.ova}" --encrypted
#
# 3. monitor import
# aws ec2 describe-import-image-tasks
#


# ho-qtp-t02vm
module "ec2_instance_ho_qtp_t02vm" {
  source  = "terraform-aws-modules/ec2-instance/aws"
  version = "5.7.1"

  name          = "ho-qtp-t02vm"
  ami           = "ami-071fc42fd606b6a8e" # import-ami-1243380b01c5d425t
  instance_type = "c4.large"
  subnet_id     = element(data.aws_subnets.platform_network_intra.ids, 0)
  vpc_security_group_ids = [
    module.security_group_internal_rdp.security_group_id,
    module.security_group_internal_smb.security_group_id
  ]

  enable_volume_tags = false
  root_block_device = [
    {

      volume_type = "gp3"
      volume_size = 60
      encrypted   = true
      tags = merge(local.default_tags, {
        Name = "ho-qtp-t02vm-root"
      })
    }
  ]

  create_iam_instance_profile = true
  iam_role_policies = {
    ssm_managed_core = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"
  }

  metadata_options = {
    http_endpoint               = "enabled"
    http_tokens                 = "required"
    http_put_response_hop_limit = 1
    instance_metadata_tags      = "enabled"
  }

  tags = merge(local.default_tags, {
    Name         = "ho-qtp-t02vm"
    "blg:backup" = "7d2w12m"
  })
}
