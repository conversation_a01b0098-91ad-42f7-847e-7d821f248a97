/*
 * Module specific locals
 *
 */

locals {

  internal_cidrs = [
    "172.25.0.0/16", # Butlins
    "172.26.0.0/16", # Warner
    "172.28.0.0/14", # Haven/Shared
    "10.0.0.0/8"     # On-Prem and VPN
  ]

  beyond_trust_cidr = "10.198.208.155/32"

  instances = {
    some-instance = {
      create          = false
      ami             = ""
      instance_type   = "t3.micro"
      security_groups = [module.security_group_internal_rdp.security_group_id]
      subnet_id       = element(data.aws_subnets.platform_network_intra.ids, 0)
      disks = [
        {
          device_name = "/dev/sda1"
          volume_type = "gp3"
          volume_size = 8
        },
      ]
      create_iam_instance_profile = true
      iam_role_policies = {
        ssm_managed_core = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"
      }
      tags = {
        Name = "some-instance"
      }
    }
  }
}

# lookup security group internal-any
data "aws_security_group" "internal_any" {
  filter {
    name   = "tag:Name"
    values = ["platform-network-internal-any"]
  }
}

