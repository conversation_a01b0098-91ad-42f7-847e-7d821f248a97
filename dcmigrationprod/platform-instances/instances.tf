
module "ec2_instances" {
  source  = "terraform-aws-modules/ec2-instance/aws"
  version = "5.7.1"

  for_each = local.instances

  create                      = lookup(each.value, "create", null)
  name                        = each.value.tags.Name
  ami                         = lookup(each.value, "ami", null)
  instance_type               = lookup(each.value, "instance_type", null)
  subnet_id                   = lookup(each.value, "subnet_id", null)
  create_iam_instance_profile = lookup(each.value, "create_iam_instance_profile", null)
  iam_role_policies           = lookup(each.value, "iam_role_policies", null)
  root_block_device           = lookup(each.value, "root_block_device", null)
  ebs_block_device            = lookup(each.value, "ebs_block_device", null)
  vpc_security_group_ids      = lookup(each.value, "security_groups", null)
  instance_tags               = lookup(each.value, "tags", null)
  enable_volume_tags          = lookup(each.value, "enable_volume_tags", null)
  monitoring                  = lookup(each.value, "monitoring", null)

  metadata_options = {
    http_endpoint               = "enabled"
    http_tokens                 = "required"
    http_put_response_hop_limit = 1
    instance_metadata_tags      = "enabled"
  }

}
