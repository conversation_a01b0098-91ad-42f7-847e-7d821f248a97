module "security_group_internal_rdp" {
  source  = "terraform-aws-modules/security-group/aws//modules/rdp"
  version = "5.1.2"

  name        = "${var.environment_name}-${var.module_name}-internal-rdp"
  description = "Security group for internal RDP within the VPC"
  vpc_id      = data.aws_vpc.platform_network.id

  ingress_cidr_blocks = local.internal_cidrs
}

module "security_group_internal_smb" {
  source  = "terraform-aws-modules/security-group/aws"
  version = "5.1.2"

  name        = "${var.environment_name}-${var.module_name}-internal-smb"
  description = "Security group for internal SMB within the VPC"
  vpc_id      = data.aws_vpc.platform_network.id

  ingress_with_cidr_blocks = [for cidr in local.internal_cidrs :
    {
      from_port   = 445
      to_port     = 445
      protocol    = "tcp"
      description = "Allow SMB access"
      cidr_blocks = cidr
    }
  ]

  egress_rules = ["all-all"]
}
