module "datadog_secrets" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  tags = local.default_tags

  kubernetes_name      = "datadog-external-secret"
  kubernetes_namespace = "datadog"

  secrets = {
    api-key = trimspace(file("${path.module}/secrets/api-key"))
    app-key = trimspace(file("${path.module}/secrets/app-key"))
    token   = trimspace(file("${path.module}/secrets/token"))
  }
}
