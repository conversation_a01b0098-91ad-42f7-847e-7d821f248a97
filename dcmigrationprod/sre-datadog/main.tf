module "dd" {
  source = "**************:HavenEngineering/tf-datadog-aws-integration?ref=v3.0.0"

  api_key = sensitive(file("${path.module}/secrets/datadog-api-key.secret"))
  app_key = sensitive(file("${path.module}/secrets/datadog-app-key.secret"))

  # This actually disables the lambda forwarder itself doing enhanced metrics
  # which is that the docs say
  enhanced_metrics_enabled = false

  cache_failed_events = true

  lambda_forwarder_timeout     = 10
  lambda_forwarder_memory_size = 256

  filter_tags = [
    "blg_provisionedby:eksctl",
    "datadog:true",
    "!datadog:false"
  ]

  module_environment = "dcmigrationprod"
  resource_name      = "dcmigrationprod-cloud"

  logs_custom_tags = [
    {
      key   = "aws_account"
      value = data.aws_caller_identity.current.account_id
    }
  ]
}
