# lookup tooling/platform-kubernetes remote state
data "terraform_remote_state" "platform_kubernetes" {
  backend = "s3"

  config = {
    bucket  = var.terraform_state_bucket
    key     = "tooling/platform-kubernetes"
    profile = var.terraform_state_profile
    region  = var.terraform_state_region
  }
}

# lookup tooling/platform-k8s remote state
data "terraform_remote_state" "platform_k8s" {
  backend = "s3"

  config = {
    bucket  = var.terraform_state_bucket
    key     = "tooling/platform-k8s"
    profile = var.terraform_state_profile
    region  = var.terraform_state_region
  }
}

# lookup AdministratorAccess iam policy
data "aws_iam_policy" "administrator" {
  name = "AdministratorAccess"
}
