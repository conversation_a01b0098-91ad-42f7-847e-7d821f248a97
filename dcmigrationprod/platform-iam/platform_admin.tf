data "aws_iam_policy_document" "platform_admin_assume_policy" {
  statement {
    actions = ["sts:AssumeRole"]

    principals {
      type = "AWS"
      identifiers = [
        data.terraform_remote_state.platform_k8s.outputs.admin_runner_role_arn
      ]
    }
  }
}

resource "aws_iam_role" "platform_admin" {
  name               = "platform-admin"
  assume_role_policy = data.aws_iam_policy_document.platform_admin_assume_policy.json

  tags = local.default_tags
}

resource "aws_iam_role_policy_attachment" "platform_admin" {
  role       = aws_iam_role.platform_admin.name
  policy_arn = data.aws_iam_policy.administrator.arn
}
