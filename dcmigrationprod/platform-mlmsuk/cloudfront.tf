resource "aws_cloudfront_distribution" "haven_mlmsuk_website" {
  origin {
    domain_name = module.haven_mlmsuk_website.bucket_domain_name
    origin_id   = "haven-mlmsuk-website"
    s3_origin_config {
      origin_access_identity = aws_cloudfront_origin_access_identity.haven_mlmsuk_website.cloudfront_access_identity_path
    }
  }

  enabled             = true
  is_ipv6_enabled     = false
  comment             = "mlmsuk.com website"
  default_root_object = "index.html"
  web_acl_id          = aws_wafv2_web_acl.haven_mlmsuk_website.arn

  aliases = ["mlmsuk.com", "www.mlmsuk.com", "test.mlmsuk.com"]

  default_cache_behavior {
    allowed_methods  = ["GET", "HEAD", "OPTIONS"]
    cached_methods   = ["GET", "HEAD"]
    target_origin_id = "haven-mlmsuk-website"

    forwarded_values {
      query_string = false

      cookies {
        forward = "none"
      }
    }

    viewer_protocol_policy = "allow-all"
    min_ttl                = 0
    default_ttl            = 3600
    max_ttl                = 86400
  }

  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }

  viewer_certificate {
    acm_certificate_arn      = aws_acm_certificate.mlmsuk_website.arn
    ssl_support_method       = "sni-only"
    minimum_protocol_version = "TLSv1.2_2021"
  }

  tags = local.default_tags
}
