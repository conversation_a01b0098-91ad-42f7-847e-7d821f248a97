module "haven_mlmsuk_website" {
  source        = "**************:HavenEngineering/tf-s3-bucket?ref=v3.0.1"
  resource_name = "haven-mlmsuk-website"

  website = {
    index_document = "index.asp"
  }

  tags = local.default_tags
}

resource "aws_cloudfront_origin_access_identity" "haven_mlmsuk_website" {
  comment = "s3://${module.haven_mlmsuk_website.bucket_name}"
}

data "aws_iam_policy_document" "haven_mlmsuk_website" {
  statement {
    actions   = ["s3:GetObject"]
    resources = ["${module.haven_mlmsuk_website.bucket_arn}/*"]

    principals {
      type        = "AWS"
      identifiers = [aws_cloudfront_origin_access_identity.haven_mlmsuk_website.iam_arn]
    }
  }
}

resource "aws_s3_bucket_policy" "haven_mlmsuk_website" {
  bucket = module.haven_mlmsuk_website.bucket_name
  policy = data.aws_iam_policy_document.haven_mlmsuk_website.json
}

output "haven_mlmsuk_website_endpoint" {
  value = module.haven_mlmsuk_website.website_endpoint
}
