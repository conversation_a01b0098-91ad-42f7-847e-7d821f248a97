locals {

  default_tags = {
    "blg:module"         = var.module_name
    "blg:service"        = try(regex("[^/]+$", "${path.cwd}"), "no-path")
    "blg:provisionedby"  = "terraform"
    "blg:brand"          = "haven"
    "blg:account"        = "aw-hav-dc-migration-prod"
    "blg:environment"    = "prod"
    "env"                = "prod"
    "blg:access"         = "restricted"
    "blg:classification" = "private"
    "blg:risk"           = "high"
    "blg:serviceowner"   = "platform"
    "tribe"              = "Cloud"
    "team"               = "Platform"
    "product"            = "CorePlatform"
    "service"            = "platform-k8s"
    "terraformrepo"      = "platform-terraform/dcmigrationprod/${try(regex("[^/]+$", "${path.cwd}"), "no-path")}"
    "tagversion"         = "2.0.0"
    "env"                = "prod"
  }

  meta = {
    environment_name = var.environment_name
    module_name      = var.module_name
    team_name        = "platform"
    tribe_name       = "platform"
  }

  team_name  = "platform"
  tribe_name = "platform"
}
