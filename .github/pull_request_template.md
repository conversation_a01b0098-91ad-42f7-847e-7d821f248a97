<!--
This template is meant to help you raise Pull Requests that are easier to
review. In general, the smaller the Pull Request, the easier it is for us to
perform a proper review and it also makes it easier to revert changes.

The body of the template is intentionally left empty as there is no good way to
structure all the different kinds of changes that can be raised in this repo.
Instead, we're listing a few things that we'd like to see:

- In the body of the pull request, please add a short description of what you
  are trying to do, if it's not outright obvious from the title.

- Make sure that the Pull Request does not change multiple environments at
  the same time (dev, staging and prod).

- If this is a change to a Cloudfront distribution, please make sure that your
  Engineering Team Lead approves this Pull Request as well. Additionally, please
  describe what your intended changes are and whether you want your responses to
  be cached.

And of course, it's always great to have atomic commits with good commit
messages that convey the what and why (for example, see this excellent article
https://cbea.ms/git-commit/).

Thank you!
-->
