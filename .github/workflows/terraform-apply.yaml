name: terraform-apply

on:
  issue_comment:
    types:
      - created

jobs:
  # before we trigger an apply run, we require an approval in the form of a
  # comment that reads `/apply`. This job performs two additional checks:
  # - that the pull request is "mergeable" (git conflicts, status checks, approvals)
  # - that the user approving the apply is an admin of this repository
  apply_approval:
    runs-on: platform-runners-admin
    if: ${{ github.event.issue.pull_request && github.event.comment.body == '/apply' }}

    steps:
      - name: react
        uses: dkershner6/reaction-action@v2
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          reaction: eyes
      # since this action is triggered by a comment, for the next steps we need
      # to know the commit sha for both base and head of the pull request in
      # which this comment appeared so that we can checkout the correct working
      # trees, similar to the plan workflow (where the event payload contains
      # the pull request information automatically)
      - name: fetch pull request information
        id: pull_request
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            let pr = await github.rest.pulls.get({
              owner: context.repo.owner,
              repo: context.repo.repo,
              pull_number: context.issue.number,
            });
            core.setOutput("base_sha", pr.data.base.sha);
            core.setOutput("head_sha", pr.data.head.sha);

      # we need to checkout the repo because we need to access the helper script
      # local module
      - uses: actions/checkout@v4
        with:
          ref: ${{ steps.pull_request.outputs.head_sha }}

      - name: check request validity
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const helpers = require('./scripts/actions-script-helpers.js');

            helpers.validateApplyCommand(github, context, core);

    outputs:
      base_sha: ${{ steps.pull_request.outputs.base_sha }}
      head_sha: ${{ steps.pull_request.outputs.head_sha }}

  setup:
    runs-on: platform-runners-admin
    needs: apply_approval

    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ needs.apply_approval.outputs.head_sha }}
          fetch-depth: 0

      - name: detect changed module path
        id: detect
        run: ./scripts/actions-detect-modules
        env:
          BASE_SHA: ${{ needs.apply_approval.outputs.base_sha }}
          HEAD_SHA: ${{ needs.apply_approval.outputs.head_sha }}

    outputs:
      module_path_matrix: ${{ steps.detect.outputs.module_path_matrix }}
      status: ${{ steps.detect.outputs.status }}

  # the apply job is run when there is a comment in the pull request that
  # satisfies the following:
  # - contains the string '/apply'
  # - is written by a user who is an OWNER of the repository
  # - setup and plan have ran successfully
  apply:
    runs-on: platform-runners-admin
    needs:
      - apply_approval
      - setup
    if: ${{ needs.setup.outputs.status == 'ok' }}
    strategy:
      matrix: ${{fromJSON(needs.setup.outputs.module_path_matrix)}}
      fail-fast: false
    defaults:
      run:
        working-directory: ${{ matrix.module_path }}
    env:
      TF_CLI_ARGS: '-no-color'

    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ needs.apply_approval.outputs.head_sha }}

      - uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const { createCommitStatus } = require('./scripts/actions-script-helpers.js');
            
            createCommitStatus({
              github,
              context,
              core,
              sha: '${{ needs.apply_approval.outputs.head_sha }}',
              state: 'pending',
              targetUrl: 'https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}',
              checkContext: 'terraform-apply / apply (${{ matrix.module_path }})'
            })

      - name: update $PATH
        run: echo "${GITHUB_WORKSPACE}/scripts" >> "${GITHUB_PATH}"

      - name: setup strongbox configuration
        run: actions-setup-strongbox
        env:
          STRONGBOX_MASTER_KEY: ${{ secrets.STRONGBOX_MASTER_KEY }}

      - name: setup SSH configuration
        run: actions-setup-ssh-config
        env:
          SSH_KEY: ${{ secrets.HAVEN_PLATFORM_BOT_SSH_KEY }}

      - name: setup AWS configuration
        run: actions-setup-aws-config

      - name: determine the proper terraform version to use
        id: terraform_version
        run: tf which-version

      - uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: "${{ steps.terraform_version.outputs.required }}"

      # tf wrapper requires node.
      - uses: actions/setup-node@v4
        with:
          node-version: '22'

      - name: init
        id: init
        run: tf init

      - name: apply
        id: apply
        run: tf apply -auto-approve

      - name: store terraform apply output
        run: |-
          cat <<'EOF' >/tmp/${{github.run_number}}.apply.txt
          ${{ steps.apply.outputs.stdout }}
          EOF

      - if: always()
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const { createCommitStatus } = require('./scripts/actions-script-helpers.js');
            
            createCommitStatus({
              github,
              context,
              core,
              sha: '${{ needs.apply_approval.outputs.head_sha }}',
              state: '${{ steps.apply.outcome == 'success' && 'success' || 'failure' }}',
              targetUrl: 'https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}',
              checkContext: 'terraform-apply / apply (${{ matrix.module_path }})'
            })

      - name: post results
        if: always()
        uses: actions/github-script@v7
        env:
          STEP_INIT_OUTCOME: ${{ steps.init.outcome }}
          STEP_INIT_STDERR: ${{ steps.init.outputs.stderr }}
          STEP_INIT_STDOUT: ${{ steps.init.outputs.stdout }}
          STEP_APPLY_OUTCOME: ${{ steps.apply.outcome }}
          STEP_APPLY_STDERR: ${{ steps.apply.outputs.stderr }}
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const helpers = require('./scripts/actions-script-helpers.js');

            helpers.commentResults(
              github,
              context,
              '${{ matrix.module_path }}',
              [
                {name: "init"},
                {name: "apply", detailedSuccess: true},
              ],
            );

  finish:
    runs-on: platform-runners-ecr
    needs:
      - apply
    if: ${{ always() && contains(fromJson('["success", "failure"]'), needs.apply.result) }}
    steps:
      - name: react
        uses: dkershner6/reaction-action@v2
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          reaction: ${{ needs.apply.result == 'success' && 'hooray' || '-1' }}
