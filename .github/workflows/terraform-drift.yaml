name: terraform-drift-platform

on:
  schedule:
    - cron: "0 0 * * 6"
  push:
    branches:
      - feat/terraform-drif
      - feat/enable-pipeline
      - fixtfversion

jobs:
  setup:
    runs-on: platform-runners-admin

    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha }}
          fetch-depth: 0

      - name: clear old results
        if: always()
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
              try {
                console.log("Fetching Comments on " + context.issue.number);
                let comments = await github.rest.issues.listComments({
                  issue_number: context.issue.number,
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                });
                console.log(comments.data.length + " comments on " + context.issue.number);
                for(let i = 0; i < comments.data.length; i++) {
                  if (comments.data[i].body.includes("terraform plan"))
                  {
                    github.rest.issues.deleteComment({
                      owner: context.repo.owner,
                      repo: context.repo.repo,
                      comment_id: comments.data[i].id,
                    });
                    console.log(comments.data[i].id + " " + comments.data[i].body);
                  }
                }
              } catch (exceptionVar) {
                console.log(exceptionVar);
              }
  dev-plan:
    runs-on: platform-runners-admin
    needs: setup
    env:
      TF_CLI_ARGS: '-no-color'

    strategy:
      matrix:
        directory: [ dev/platform-tailscale-legacy, dev/platform-auth, dev/platform-base, dev/platform-cloudfront-devprod, dev/platform-cloudfront-subdomains, dev/platform-cloudfront-subdomains-staging, dev/platform-cloudfront-www, dev/platform-dns, dev/platform-ecr, dev/platform-elastic, dev/platform-global, dev/platform-iam, dev/platform-k8s, dev/platform-kubernetes, dev/platform-monitoring, dev/platform-network, dev/platform-runners, dev/platform-ses, dev/platform-tailscale, dev/platform-wiz, dev/foundation-contact-preferences, dev/foundation-crm-smtp-router, dev/foundation-event-generation, dev/foundation-identity, dev/foundation-payments, dev/foundation-revenuemanagement, dev/foundation-seaware-account-manager, dev/holidaysales-checkout-gateway, dev/holidaysales-cyhh, dev/holidaysales-giftcard, dev/holidaysales-land-and-discover, dev/holidaysales-opensearch, dev/holidaysales-search, dev/holidaysales-session-gateway, dev/sales-caravan-sales, dev/sales-integrations, dev/shared-caravan-imagery, dev/shared-cloudfront-static-assets, dev/shared-eventbus, dev/shared-payments-barclaycard, dev/shared-playpass, dev/shared-services, dev/shared-ses, dev/shared-zendesk, dev/sre-datadog, dev/sre-maintenance-page]

    defaults:
      run:
        working-directory: ${{ matrix.directory }}

    steps:
      - uses: actions/checkout@v4

      - name: update $PATH
        run: echo "${GITHUB_WORKSPACE}/scripts" >> "${GITHUB_PATH}"

      - name: setup strongbox configuration
        run: actions-setup-strongbox
        env:
          STRONGBOX_MASTER_KEY: ${{ secrets.STRONGBOX_MASTER_KEY }}

      - name: setup SSH configuration
        run: actions-setup-ssh-config
        env:
          SSH_KEY: ${{ secrets.HAVEN_PLATFORM_BOT_SSH_KEY }}

      - name: setup AWS configuration
        run: actions-setup-aws-config

      - name: determine the proper terraform version to use
        id: terraform_version
        run: tf which-version
        
      - uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: "${{ steps.terraform_version.outputs.required }}"
      - uses: actions/setup-node@v4
        with:
          node-version: '22'

      - name: checking drift in dev folders
        id: plan
        working-directory: ${{ matrix.directory }}
        run: |
         pwd
         tf init
         tf plan -input=false -no-color -detailed-exitcode
         retVal=$?
         echo $retVal
         if [ $retVal == 2 ]; then
            echo "Changes Found"
          else
            echo "Change not Found"
         fi
         cd ..


  staging-plan:
    runs-on: platform-runners-admin
    needs: setup
    env:
      TF_CLI_ARGS: '-no-color'

    strategy:
      matrix:
        directory: [ staging/foundation-seaware, staging/holidaysales-land-and-discover, staging/ownership-cloudfront, staging/ownership-owners, staging/platform-auth, staging/platform-cloudfront-www, staging/platform-dns, staging/platform-ecr, staging/platform-iam, staging/platform-k8s, staging/platform-kubernetes, staging/platform-monitoring, staging/platform-network, staging/platform-tailscale, staging/shared-cloudfront-static-assets] 

    defaults:
      run:
        working-directory: ${{ matrix.directory }}

    steps:
      - uses: actions/checkout@v4

      - name: update $PATH
        run: echo "${GITHUB_WORKSPACE}/scripts" >> "${GITHUB_PATH}"

      - name: setup strongbox configuration
        run: actions-setup-strongbox
        env:
          STRONGBOX_MASTER_KEY: ${{ secrets.STRONGBOX_MASTER_KEY }}

      - name: setup SSH configuration
        run: actions-setup-ssh-config
        env:
          SSH_KEY: ${{ secrets.HAVEN_PLATFORM_BOT_SSH_KEY }}

      - name: setup AWS configuration
        run: actions-setup-aws-config

      - name: determine the proper terraform version to use
        id: terraform_version
        run: tf which-version

      - uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: "${{ steps.terraform_version.outputs.required }}"
      - uses: actions/setup-node@v4
        with:
          node-version: '22'

      - name: checking drift in dev folders
        id: plan
        working-directory: ${{ matrix.directory }}
        run: |
         pwd
         tf init
         tf plan -input=false -no-color -detailed-exitcode
         retVal=$?
         echo $retVal
         if [ $retVal == 2 ]; then
            echo "Changes Found"
          else
            echo "Change not Found"
         fi
         cd ..


  tooling-plan:
    runs-on: platform-runners-admin
    needs: setup
    env:
      TF_CLI_ARGS: '-no-color'

    strategy:
      matrix:
        directory: [tooling/activities, tooling/caravan-images, tooling/cms, tooling/cms-data, tooling/contact-centre, tooling/cyhh, tooling/eat, tooling/entertainment, tooling/exp-bookings, tooling/experience-aac, tooling/experience-shared, tooling/food-and-beverage, tooling/foundation-contact-preferences, tooling/foundation-data, tooling/foundation-identity, tooling/foundation-payments, tooling/foundations-event-generation, tooling/guest, tooling/one-team-hub, tooling/owners, tooling/owners-cards-and-rewards, tooling/ownership-lettings, tooling/ownership-owners, tooling/platform, tooling/platform-argocd, tooling/platform-auth, tooling/platform-backstage, tooling/platform-dns, tooling/platform-ecr, tooling/platform-github-state, tooling/platform-global, tooling/platform-iam, tooling/platform-k8s, tooling/platform-kubernetes, tooling/platform-logging, tooling/platform-monitoring, tooling/platform-network, tooling/platform-runners, tooling/platform-tailscale, tooling/platform-terraform-state, tooling/platform-wiz, tooling/revenue-management, tooling/sales-checkout, tooling/sales-cyhh, tooling/sales-integrations, tooling/sales-search, tooling/session, tooling/shared-services, tooling/smtp-router, tooling/sre-apps, tooling/sre-datadog] 

    defaults:
      run:
        working-directory: ${{ matrix.directory }}

    steps:
      - uses: actions/checkout@v4

      - name: update $PATH
        run: echo "${GITHUB_WORKSPACE}/scripts" >> "${GITHUB_PATH}"

      - name: setup strongbox configuration
        run: actions-setup-strongbox
        env:
          STRONGBOX_MASTER_KEY: ${{ secrets.STRONGBOX_MASTER_KEY }}

      - name: setup SSH configuration
        run: actions-setup-ssh-config
        env:
          SSH_KEY: ${{ secrets.HAVEN_PLATFORM_BOT_SSH_KEY }}

      - name: setup AWS configuration
        run: actions-setup-aws-config

      - name: determine the proper terraform version to use
        id: terraform_version
        run: tf which-version  

      - uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: "${{ steps.terraform_version.outputs.required }}"
      - uses: actions/setup-node@v4
        with:
          node-version: '22'

      - name: checking drift in dev folders
        id: plan
        working-directory: ${{ matrix.directory }}
        run: |
         pwd
         tf init
         tf plan -input=false -no-color -detailed-exitcode
         retVal=$?
         echo $retVal
         if [ $retVal == 2 ]; then
            echo "Changes Found"
          else
            echo "Change not Found"
         fi
         cd ..



