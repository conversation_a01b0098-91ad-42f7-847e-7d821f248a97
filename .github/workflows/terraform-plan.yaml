name: terraform-plan

on:
  pull_request:
    branches:
      - master

jobs:
  setup:
    runs-on: platform-runners-admin-arm

    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha }}
          fetch-depth: 0

      - name: detect changed module path
        id: detect
        run: ./scripts/actions-detect-modules
        env:
          BASE_SHA: ${{ github.event.pull_request.base.sha }}
          HEAD_SHA: ${{ github.event.pull_request.head.sha }}

      - name: clear old results
        if: always()
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
              try {
                console.log("Fetching Comments on " + context.issue.number);
                let comments = await github.rest.issues.listComments({
                  issue_number: context.issue.number,
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                });
                console.log(comments.data.length + " comments on " + context.issue.number);
                for(let i = 0; i < comments.data.length; i++) {
                  if (comments.data[i].body.includes("terraform plan"))
                  {
                    github.rest.issues.deleteComment({
                      owner: context.repo.owner,
                      repo: context.repo.repo,
                      comment_id: comments.data[i].id,
                    });
                    console.log(comments.data[i].id + " " + comments.data[i].body);
                  }
                }
              } catch (exceptionVar) {
                console.log(exceptionVar);
              }

    outputs:
      module_path_matrix: ${{ steps.detect.outputs.module_path_matrix }}
      status: ${{ steps.detect.outputs.status }}

  plan:
    runs-on: platform-runners-admin-arm
    needs: setup
    if: ${{ needs.setup.outputs.status == 'ok' }}
    strategy:
      matrix: ${{fromJSON(needs.setup.outputs.module_path_matrix)}}
      fail-fast: false
    defaults:
      run:
        working-directory: ${{ matrix.module_path }}
    env:
      TF_CLI_ARGS: '-no-color'

    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha }}

      # this setup simply allows us to invoke scripts in the ./scripts directory
      # directly, without specifying a path
      - name: update $PATH
        run: echo "${GITHUB_WORKSPACE}/scripts" >> "${GITHUB_PATH}"

      - name: setup strongbox configuration
        run: actions-setup-strongbox
        env:
          STRONGBOX_MASTER_KEY: ${{ secrets.STRONGBOX_MASTER_KEY }}

      - name: setup SSH configuration
        run: actions-setup-ssh-config
        env:
          SSH_KEY: ${{ secrets.HAVEN_PLATFORM_BOT_SSH_KEY }}

      - name: setup AWS configuration
        run: actions-setup-aws-config

      - name: determine the proper terraform version to use
        id: terraform_version
        run: tf which-version

      - uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: "${{ steps.terraform_version.outputs.required }}"
      # tf wrapper requires node.
      - uses: actions/setup-node@v4
        with:
          node-version: '22'

      - name: fmt
        id: fmt
        run: tf fmt -check 1>&2

      - name: init
        id: init
        run: tf init

      - name: validate
        id: validate
        run: tf validate

      - uses: terraform-linters/setup-tflint@v3
        name: Setup TFLint
        with:
          tflint_version: v0.45.0

      - name: Init tflint
        run: tflint --init
      
      - name: tflint
        id: tflint
        run: tflint --force --format compact > /tmp/${{github.run_number}}.tflint.txt

      - name: plan
        id: plan
        run: tf plan

      - name: store terraform plan output
        run: |-
          cat <<'EOF' >/tmp/${{github.run_number}}.plan.txt
          ${{ steps.plan.outputs.stdout }}
          EOF

      - name: Check tribe affiliations and notify
        run: |
          send_teams_notification() {
            local TRIBE_NAME=$1
            local TEAM_ID=$2
            local RESOURCE_INFO=$(grep -B1 "tribe_name.*$TRIBE_NAME" _meta.tf | grep -v tribe_name | tr -d '"' | sed 's/^[[:space:]]*//')
 
            echo '{
              "type": "message",
              "summary": "'"${TRIBE_NAME}"' Team Review Required",
              "attachments": [
                {
                  "contentType": "application/vnd.microsoft.card.adaptive",
                  "content": {
                    "type": "AdaptiveCard",
                    "$schema": "https://adaptivecards.io/schemas/adaptive-card.json",
                    "version": "1.0",
                    "body": [
                      {
                        "type": "TextBlock",
                        "text": "🔍 '"${TRIBE_NAME}"' Team Review Required",
                        "weight": "bolder",
                        "size": "large"
                      },
                      {
                        "type": "TextBlock",
                        "text": "Hey <at>'"${TEAM_ID}"'</at> - New infrastructure changes require your review.",
                        "wrap": true
                      },
                      {
                        "type": "FactSet",
                        "facts": [
                          {
                            "title": "Pull Request",
                            "value": "'"${GITHUB_SERVER_URL}/${GITHUB_REPOSITORY}/pull/${PR_NUMBER}"'"
                          },
                          {
                            "title": "Resources with tribe_name:'"${TRIBE_NAME}"' tag",
                            "value": "'"${RESOURCES_LIST}"'"
                          }
                        ]
                      }
                    ],
                    "actions": [
                      {
                        "type": "Action.OpenUrl",
                        "title": "View Pull Request",
                        "url": "'"${GITHUB_SERVER_URL}/${GITHUB_REPOSITORY}/pull/${PR_NUMBER}"'"
                      }
                    ],
                    "msteams": {
                      "entities": [
                        {
                          "type": "mention",
                          "text": "<at>'"${TEAM_ID}"'</at>",
                          "mentioned": {
                            "id": "'"${TEAM_ID}"'",
                            "name": "'"${TEAM_ID}"'"
                          }
                        }
                      ]
                    }
                  }
                }
              ]
            }' > notification.json
            
            RESPONSE=$(curl -s -w "\n%{http_code}" -H "Content-Type: application/json" \
                           -d @notification.json \
                           "${{ secrets.PLAN_TEAMS_WEBHOOK }}")
            
            HTTP_STATUS=$(echo "$RESPONSE" | tail -n1)
            RESPONSE_BODY=$(echo "$RESPONSE" | head -n1)
            
            if [ "$HTTP_STATUS" -eq 200 ]; then
              echo "Notification sent successfully to ${TEAM_ID}"
            else
              echo "Failed to send notification to ${TEAM_ID}. Status: $HTTP_STATUS"
              echo "Response: $RESPONSE_BODY"
              exit 1
            fi
          }

          if [ -f _meta.tf ]; then
            echo "Checking _meta.tf for tribe affiliations..."
            
            if grep -q 'tribe_name[[:space:]]*=[[:space:]]*"foundation"' _meta.tf; then
              echo "Found foundation tribe tag"
              #send_teams_notification "Foundation" "cloudeng-foundation" "$(grep -B1 'tribe_name.*foundation' _meta.tf | grep -v tribe_name)"
            fi
            
            if grep -q 'tribe_name[[:space:]]*=[[:space:]]*"experience"' _meta.tf; then
              echo "Found experience tribe tag"
              #send_teams_notification "Experience" "cloudeng-experience" "$(grep -B1 'tribe_name.*experience' _meta.tf | grep -v tribe_name)"
            fi
            
            if grep -q 'tribe_name[[:space:]]*=[[:space:]]*"sales"' _meta.tf; then
              echo "Found sales tribe tag"
              #send_teams_notification "Sales" "cloudeng-sales" "$(grep -B1 'tribe_name.*sales' _meta.tf | grep -v tribe_name)"
            fi
          else
            echo "_meta.tf file not found"
            exit 1
          fi
        env:
          PR_NUMBER: ${{ github.event.pull_request.number }}

      - name: post results
        if: always()
        uses: actions/github-script@v7
        env:
          STEP_FMT_OUTCOME: ${{ steps.fmt.outcome }}
          STEP_FMT_STDERR: ${{ steps.fmt.outputs.stderr }}
          STEP_FMT_STDOUT: ${{ steps.fmt.outputs.stdout }}
          STEP_INIT_OUTCOME: ${{ steps.init.outcome }}
          STEP_INIT_STDERR: ${{ steps.init.outputs.stderr }}
          STEP_INIT_STDOUT: ${{ steps.init.outputs.stdout }}
          STEP_VALIDATE_OUTCOME: ${{ steps.validate.outcome }}
          STEP_VALIDATE_STDERR: ${{ steps.validate.outputs.stderr }}
          STEP_VALIDATE_STDOUT: ${{ steps.validate.outputs.stdout }}
          STEP_TFLINT_OUTCOME: ${{ steps.tflint.outcome }}
          STEP_TFLINT_STDERR: ${{ steps.tflint.outputs.stderr }}
          STEP_PLAN_OUTCOME: ${{ steps.plan.outcome }}
          STEP_PLAN_STDERR: ${{ steps.plan.outputs.stderr }}
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const helpers = require('./scripts/actions-script-helpers.js');

            helpers.commentResults(
              github,
              context,
              '${{ matrix.module_path }}',
              [
                {name: "fmt"},
                {name: "init"},
                {name: "validate"},
                {name: "tflint", detailedSuccess: true},                
                {name: "plan", detailedSuccess: true},
              ],
            );
