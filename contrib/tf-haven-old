#!/bin/bash
#
# Used for repositories where the layout is like this:
# https://github.com/HavenEngineering/service-haven-holiday-search-ingest/tree/master/infrastructure/terraform
#
# Make sure you have the aws cli tool installed and configured, as well as
# terraform v0.14.8 downloaded in your path as "terraform-0.14.8"
#

set -o errexit
set -o pipefail
set -o nounset

[ -z "${1:-}" ] && { echo "usage: $(basename "${0}") <env> TF_ARGS..."; exit 1; }

tf="terraform-${tf_ver:-0.14.8}"
env=$1
shift

name="$(basename "$(git rev-parse --show-toplevel)")"

bucket=blg-development-terraform-state
[ "${env}" == "prod" ] && bucket=blg-production-terraform-state

AWS_PROFILE="bourne-${env}"
[ "${env}" == "staging" ] && AWS_PROFILE="bourne-dev"
export AWS_PROFILE
export TF_VAR_env="${env}"

tfcmd="${1}"
shift

if [ "${tfcmd}" == "init" ]; then
	$tf init \
		-backend-config="bucket=${bucket}" \
		-backend-config="key=deployments/${name}/${env}/terraform.tfstate" \
		-reconfigure

	aws s3 cp \
		"s3://${bucket}/deployments/${name}/${env}/terraform.tfvars" \
		"/data/terraform.${env}.tfvars"

	exit 0
fi

$tf $tfcmd \
	-var-file="terraform.${env}.tfvars" \
	"${@}"

if [ "${tfcmd}" == "apply" ]; then
	aws s3 cp \
		"/data/terraform.${env}.tfvars" \
		"s3://${bucket}/deployments/${name}/${env}/terraform.tfvars"
fi

if [ "${tfcmd}" == "destroy" ]; then
	read -p "Press enter to remove the remote state and tfvars, Ctrl+C to stop..."
	aws s3 rm "s3://${bucket}/deployments/${name}/${env}/terraform.tfstate"
	aws s3 rm "s3://${bucket}/deployments/${name}/${env}/terraform.tfvars"
fi
