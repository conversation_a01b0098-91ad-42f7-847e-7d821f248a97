#!/usr/bin/env bash
#
# Shows a list of the busiest destinations for the NAT Gateways associated with
# a VPC. Has only been used with the legacy VPCs and includes a number of
# hardcoded names and values so it will probably need adjusting to work for the
# new VPCs, which have predictable names.
#
# Usage: ./nat-destination <vpc-id> [timeframe]
#   eg.: ./nat-destination vpc-0123456789abcdef0 "7 days ago"
#
# Requires ENI VPC flow logs to be enabled for the NAT Gateway ENIs.
#

set -o errexit
set -o pipefail
set -o nounset

limit=20

function destination_name() {
	(openssl s_client -showcerts -connect "${1}:443" </dev/null | rg -r '$1' 'subject=.*CN = ([^\s]+)') 2>/dev/null || echo "n/a"
}

function enis_for_vpc() {
	aws ec2 describe-network-interfaces \
		--filters "Name=vpc-id,Values=${1}" 'Name=description,Values=Interface for NAT Gateway nat-*' \
		| jq -c '[.NetworkInterfaces[] | .NetworkInterfaceId]'
}

read -r -d '' logs_query <<EOF || true
filter (interfaceId in $(enis_for_vpc "${1}") and dstAddr not like "172.31.")
| stats sum(bytes) as bytesTransferred by dstAddr
| sort bytesTransferred desc
| limit ${limit}
EOF

log_group=$(aws logs describe-log-groups \
	| jq -r '.logGroups[] | select(.logGroupName | test(".*-legacy-nat-nic-flow-logs")) | .logGroupName')

span="${2:-24 hours ago}"
query_id=$(aws logs start-query \
	--start-time "$(date --date="${span}" +%s)" \
	--end-time "$(date +%s)" \
	--log-group-name "${log_group}" \
	--query-string "${logs_query}" \
	| jq -r .queryId)

while true; do
	sleep 5
	status=$(aws logs get-query-results --query-id "${query_id}" | jq -r '.status')
	if [ "${status}" == "Running" ]; then echo ">>> query running..."; continue; fi
	[ "${status}" != "Complete" ] && echo ">>> query status: ${status}" && exit 1
	echo ">>> query completed"
	break
done

readarray parsed < <(aws logs get-query-results \
	--query-id "${query_id}" \
	| jq -rc '.results[] | (.[] | select(.field == "dstAddr") | .value) + " " + (.[] | select(.field == "bytesTransferred") | .value)'
)

declare -A sums=()
out=""
for item in "${parsed[@]}"; do
	item=$(echo "${item}" | tr --delete '\n')
	dst=$(echo "${item}" | cut -f1 -d' ')
	tx=$(echo "${item}" | cut -f2 -d' ')

	name=$(destination_name "${dst}")
	bytes=$(echo "${tx}" | numfmt --to=iec)

	sld="$(echo "${name}" | rg -r '$1' '.*\.(([^.]+)\.([^.]+)$)' || echo "${name}")"
	sums[${sld}]=$((${sums[${sld}]:-0} + tx))

	out+="${dst} ${name} ${bytes}\n"
done

echo ">>> top ${limit}"
echo -e "${out}" | column -t

outsums=""
for i in "${!sums[@]}"; do
	outsums+="${i} $(echo ${sums[${i}]} | numfmt --to=iec)\n"
done

echo -e "\n>>> sum"
echo -e "${outsums}" | column -t
