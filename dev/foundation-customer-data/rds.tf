module "postgres_service_haven_customer_data_gateway" {
  source        = "**************:HavenEngineering/tf-rds-instance?ref=v4.2.0"
  resource_name = "dev-foundation-customer-data"

  engine_version     = "16.8"
  instance_type      = "db.t4g.small"
  parameters_family  = "postgres16"
  source_cidr_blocks = local.dev_db_default_allowed_cidrs

  multi_az                  = false
  disable_cloudwatch_alarms = true
  cloudwatch_actions = {
    warning  = ""
    critical = ""
  }
  additional_instance_tags = {
    "StartInstance" : base64encode("cron(0 2 ? * WED *)|cron(50 5 ? * MON-FRI *)")
    "StopInstance" : base64encode("cron(0 3 ? * WED *)|cron(05 20 ? * MON-FRI *)")
    "StopOverride" : "dd.mm.yyyy"
  }
}
