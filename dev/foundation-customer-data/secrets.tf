resource "random_password" "service_haven_customer_data_gateway_api_keys_for_consumers" {
  for_each = toset([
    "service-haven-holiday-my-account",
  ])
  length  = 16
  special = false
}

# service-haven-customer-data-gateway
module "secrets_service_haven_customer_data_gateway" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "service-haven-customer-data-gateway"
  kubernetes_namespace = "dev-customer-data"

  secrets = {
    DATABASE_URL = "postgresql://${module.postgres_service_haven_customer_data_gateway.db_username}:${module.postgres_service_haven_customer_data_gateway.db_password}@${module.postgres_service_haven_customer_data_gateway.db_endpoint}/${module.postgres_service_haven_customer_data_gateway.db_name}?schema=public"
    API_KEYS     = jsonencode([for _, v in random_password.service_haven_customer_data_gateway_api_keys_for_consumers : v.result])
  }
}