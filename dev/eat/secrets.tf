data "terraform_remote_state" "experience_activities" {
  backend = "s3"

  config = {
    bucket  = var.terraform_state_bucket
    key     = "dev/experience-activities"
    profile = var.terraform_state_profile
    region  = var.terraform_state_region
  }
}

data "terraform_remote_state" "ownership_owners" {
  backend = "s3"

  config = {
    bucket  = var.terraform_state_bucket
    key     = "dev/ownership-owners"
    profile = var.terraform_state_profile
    region  = var.terraform_state_region
  }
}

data "terraform_remote_state" "experience_aac" {
  backend = "s3"

  config = {
    bucket  = var.terraform_state_bucket
    key     = "dev/experience-aac"
    profile = var.terraform_state_profile
    region  = var.terraform_state_region
  }
}

data "terraform_remote_state" "comms" {
  backend = "s3"

  config = {
    bucket  = var.terraform_state_bucket
    key     = "dev/comms"
    profile = var.terraform_state_profile
    region  = var.terraform_state_region
  }
}

data "terraform_remote_state" "shared_playpass" {
  backend = "s3"

  config = {
    bucket  = var.terraform_state_bucket
    key     = "dev/shared-playpass"
    profile = var.terraform_state_profile
    region  = var.terraform_state_region
  }
}

data "terraform_remote_state" "venues" {
  backend = "s3"

  config = {
    bucket  = var.terraform_state_bucket
    key     = "dev/venues"
    profile = var.terraform_state_profile
    region  = var.terraform_state_region
  }
}

locals {
  activities_service_management_api_key = data.terraform_remote_state.experience_activities.outputs.activities_service_management_api_key
  sid_auth_secret                       = data.terraform_remote_state.experience_activities.outputs.sid_auth_secret
  owners_account_api_keys               = data.terraform_remote_state.ownership_owners.outputs.service_haven_owners_account_api_keys
  wifi_service_api_key                  = data.terraform_remote_state.experience_aac.outputs.wifi_service_management_api_key
  comms_api_key                         = data.terraform_remote_state.comms.outputs.service_haven_comms_api_key
  shared_playpass_api_keys              = data.terraform_remote_state.shared_playpass.outputs.api_keys
  venues_gateway_api_key                = data.terraform_remote_state.venues.outputs.venues_gateway_auth_key
}

module "secrets_service_eat_gateway" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "service-eat-gateway"
  kubernetes_namespace = "dev-exp"

  secrets = {
    SID_AUTH_SECRET                          = local.sid_auth_secret
    API_KEY_ACTIVITIES_SERVICE_MANAGEMENT    = local.activities_service_management_api_key
    API_KEY_COMMS                            = local.comms_api_key
    API_KEY_OWNERS_ACCOUNT                   = local.owners_account_api_keys["service-haven-experience-admin-owners-gateway"] // TODO replace with service-eat-gateway
    API_KEY_WIFI                             = local.wifi_service_api_key
    API_KEY_ZONAL_IORDER                     = trimspace(file("${path.module}/secrets/api-key-zonal-iorder.secret"))
    API_KEY_CONTENTFUL_EXPERIENCE            = trimspace(file("${path.module}/../common-secrets/secrets/contentful/contentful-api-delivery-experience-dev.secret"))
    API_KEY_CONTENTFUL_MANAGEMENT_EXPERIENCE = trimspace(file("${path.module}/../common-secrets/secrets/contentful/contentful-cma-token.secret"))
    API_KEY_PLAYPASS                         = local.shared_playpass_api_keys["service-eat-gateway"]
    API_KEY_VENUES_GATEWAY                   = local.venues_gateway_api_key
  }
}
