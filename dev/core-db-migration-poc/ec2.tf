# ami
data "aws_ami" "windows_sql" {
  most_recent = true
  filter {
    name   = "name"
    values = ["Windows_Server-2022-English-Full-SQL_2022_Express*"]
  }
  owners = ["amazon"]
}

# temp instance for db migration testing
module "temp_ec2_instance" {
  source  = "terraform-aws-modules/ec2-instance/aws"
  version = "5.6.1"

  name = "${var.environment_name}-${var.module_name}"

  ami           = data.aws_ami.windows_sql.id
  instance_type = "t3.large"
  subnet_id     = element(data.aws_subnets.platform_network_private.ids, 0)
  vpc_security_group_ids = [
    module.security_group_internal_rdp.security_group_id
  ]

  create_iam_instance_profile = true
  iam_role_description        = "IAM role for EC2 instance ${var.environment_name}-${var.module_name}"
  iam_role_policies = {
    ssm_managed_core = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"
  }

  enable_volume_tags = true
  root_block_device = [
    {
      encrypted   = true
      volume_type = "gp3"
      throughput  = 125
      volume_size = 85
    }
  ]

  instance_tags = {
    "StartInstance" : "cron(0 8 ? * MON-FRI *)"
    "StopInstance" : "cron(0 18 ? * MON-FRI *)"
    "StopOverride" : "dd.mm.yyyy"
    "map-migrated" : "d-server-02psycez4uvd3l"
    "aws-migration-project-id" : "MPE16054"
  }
}
