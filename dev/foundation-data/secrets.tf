module "secrets_service-haven-data-pricing-ingestion" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "service-haven-data-pricing-ingestion"
  kubernetes_namespace = "foundation-data"

  secrets = {
    BLAPI_API_KEY         = trimspace(file("${path.module}/secrets/blapi-api-key.secret"))
    SNOWFLAKE_EXTERNAL_ID = trimspace(file("${path.module}/secrets/snowflake-external-id.secret"))
  }
}

module "secrets_service-data-mwaa-dags" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "service-data-mwaa-dags"
  kubernetes_namespace = "foundation-data-mwaa"

  secrets = {
    SF_DEV_ACCOUNT  = trimspace(file("${path.module}/secrets/snowflake-dev-account.secret"))
    SF_DEV_USER     = trimspace(file("${path.module}/secrets/snowflake-dev-user.secret"))
    SF_DEV_KEY      = trimspace(file("${path.module}/secrets/snowflake-dev-key.secret"))
    SF_QA_ACCOUNT   = trimspace(file("${path.module}/secrets/snowflake-qa-account.secret"))
    SF_QA_USER      = trimspace(file("${path.module}/secrets/snowflake-qa-user.secret"))
    SF_QA_KEY       = trimspace(file("${path.module}/secrets/snowflake-qa-key.secret"))
    SF_PROD_ACCOUNT = trimspace(file("${path.module}/secrets/snowflake-prod-account.secret"))
    SF_PROD_USER    = trimspace(file("${path.module}/secrets/snowflake-prod-user.secret"))
    SF_PROD_KEY     = trimspace(file("${path.module}/secrets/snowflake-prod-key.secret"))
  }
}
