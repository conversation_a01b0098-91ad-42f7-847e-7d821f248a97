data "aws_cloudfront_cache_policy" "managed_caching_disabled" {
  name = "Managed-CachingDisabled"
}

data "aws_cloudfront_cache_policy" "managed_caching_optimized" {
  name = "Managed-CachingOptimized"
}

resource "aws_cloudfront_cache_policy" "custom_origin_caching" {
  name        = "Custom-OriginCaching"
  comment     = "Add origin related headers into the cache key to avoid the CORS errors"
  default_ttl = 86400
  max_ttl     = 31536000
  min_ttl     = 1
  parameters_in_cache_key_and_forwarded_to_origin {
    cookies_config {
      cookie_behavior = "none"
    }
    headers_config {
      header_behavior = "whitelist"
      headers {
        items = ["origin", "access-control-request-headers", "access-control-request-method"]
      }
    }
    query_strings_config {
      query_string_behavior = "none"
    }
    enable_accept_encoding_brotli = true
    enable_accept_encoding_gzip   = true
  }
}
resource "aws_cloudfront_cache_policy" "ddos_protection" {
  name        = "DDOS-Protection"
  comment     = "Minimal caching to protect us from DDOS attacks"
  default_ttl = 10
  max_ttl     = 10
  min_ttl     = 1
  parameters_in_cache_key_and_forwarded_to_origin {
    cookies_config {
      cookie_behavior = "whitelist"
      cookies {
        items = ["__prerender_bypass"]
      }
    }
    headers_config {
      header_behavior = "whitelist"
      headers {
        items = ["RSC", "Next-Router-State-Tree", "Next-Router-Prefetch", "Next-Router-Segment-Prefetch"]
      }
    }
    query_strings_config {
      query_string_behavior = "none"
    }
    enable_accept_encoding_brotli = true
    enable_accept_encoding_gzip   = true
  }
}

resource "aws_cloudfront_response_headers_policy" "ddos_protection" {
  name    = "DDOS-Protection"
  comment = "Required Vary header to make caching work with Next.js."

  custom_headers_config {
    items {
      header   = "Vary"
      override = true
      value    = "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch, Accept-Encoding"
    }
  }
}

/*
  This 'response headers policy' is added to all cloudfront behaviours which formally used to serve content from buckets using the buckets s3 website endpoint.
  Those buckets had that configuration as it was the only way to enforce CORS headers on the s3 buckets. As of 2021, the below method is available for Cloudfront now on a behaviour by behaviour basis.
  This applies to buckets defined in the 'platform-terraform/shared-s3-static-assets' module, which will have those public websites removed once this change has been proven to work.
  */

resource "aws_cloudfront_response_headers_policy" "static_assets" {
  name    = "static-assets"
  comment = "Used to return CORS headers for static assets defined in platform-terraform shared-cloudfront-static-assets module."

  # this custom header is used purely to prove that our new policy is returning the headers we expect, and is only being added for diagnostic purposes
  custom_headers_config {
    items {
      header   = "X-Test"
      override = true
      value    = "TestHead"
    }
  }
  cors_config {
    access_control_allow_credentials = false

    access_control_allow_headers {
      items = [
        "*"
      ]
    }

    access_control_allow_methods {
      items = [
        "GET",
        "HEAD"
      ]
    }

    access_control_allow_origins {
      items = [
        "https://www.haven.com",
        "https://*.digitaldevs.co.uk",
        "https://*.haven.com",
        "https://havenengineering.github.io",
        "http://localhost:*",
      ]
    }

    origin_override = false
  }
}

resource "aws_cloudfront_cache_policy" "custom_origin_caching_seven_days" {
  name        = "Custom-OriginCachingSevenDays"
  comment     = "Add origin related headers into the cache key to avoid the CORS errors"
  default_ttl = 604800
  max_ttl     = 31536000
  min_ttl     = 1
  parameters_in_cache_key_and_forwarded_to_origin {
    cookies_config {
      cookie_behavior = "none"
    }
    headers_config {
      header_behavior = "whitelist"
      headers {
        items = ["origin", "access-control-request-headers", "access-control-request-method"]
      }
    }
    query_strings_config {
      query_string_behavior = "none"
    }
    enable_accept_encoding_brotli = true
    enable_accept_encoding_gzip   = true
  }
}

data "aws_cloudfront_origin_request_policy" "managed_all_viewer" {
  name = "Managed-AllViewer"
}

/*
 * Due to how terraform handles diffs and tries to be concise about the changes
 * (in an effort to avoid lengthy diffs), when an ordered_cache_behaviour has a
 * diff, it becomes impossible to tell which one it is, since the identifying
 * attribute, path_pattern, is hidden (since it hasn't changed). For example,
 * you might see a diff like:
 *
 *   ~ ordered_cache_behavior {
 *       ~ compress               = false -> true
 *         # (11 unchanged attributes hidden)
 *
 *         # (1 unchanged block hidden)
 *     }
 *
 * Until terraform provides a way to request a verbose plan (see this issue for
 * the relevant discussion: https://github.com/hashicorp/terraform/issues/27547)
 * we can employ a hack, to check locally:
 *
 *   sed -i -E 's/^(\s*path_pattern\s*=\s*)"([^"]+)"$/\1"> \2"/' behaviors.tf
 *
 *
 * This will prepend all path_pattern values with a character (>) and cause all
 * ordered_cache_behaviors to produce a diff _including_ the path_pattern value.
 * Although cumbersome, this is a simple way to tell which behavior might have
 * drifted from the configuration defined here. Once done, you can revert this
 * hack with:
 *
 *   sed -i -E 's/(path_pattern\s*=\s*")> /\1/' behaviors.tf
 *
 */
locals {
  /*
   * Default attributes for behaviors configured in the distributions.
   * They are selected based on the AWS Cloudfront default values and adjusted
   * to minimise the configuration needed for the majority of behaviors defined.
   *
   */
  default_behavior_config = {
    allowed_methods            = ["GET", "HEAD"]
    cached_methods             = ["GET", "HEAD"]
    compress                   = true
    default_ttl                = 86400
    max_ttl                    = 31536000
    min_ttl                    = 0
    cache_policy_id            = ""
    origin_request_policy_id   = ""
    response_headers_policy_id = ""
    viewer_protocol_policy     = "redirect-to-https"
    forwarded_values = {
      headers = [
        "Authorization",
        "Host",
      ]
      query_string            = true
      query_string_cache_keys = []
      cookies = {
        forward           = "all"
        whitelisted_names = []
      }
    }
    lambda_function_associations = []
  }

  /*
   * Behaviors for the Cloudfront distribution. Each item in this list, merged
   * with the default settings above, defines a new behavior for the
   * distribution.
   *
   */
  behaviors = [
    {
      path_pattern     = "*/mraid.js"
      target_origin_id = "s3-shared-cloudfront-404-non-http"
      allowed_methods  = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      compress         = false
      default_ttl      = 0
      max_ttl          = 0
      forwarded_values = {
        headers = []
      }
      # response_headers_policy_id = resource.aws_cloudfront_response_headers_policy.static_assets.id
    },
    {
      path_pattern     = "/haven/*/direct/v2/common/holidaysearch"
      target_origin_id = "Custom-dev.blgapi.co.uk"
      allowed_methods  = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      compress         = false
      default_ttl      = 0
      max_ttl          = 0
      forwarded_values = {
        headers = []
      }
    },
    {
      path_pattern     = "/haven/*/direct/v2/common/holidaysearch/availabilityprice/calendar"
      target_origin_id = "Custom-dev.blgapi.co.uk"
      allowed_methods  = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      compress         = false
      default_ttl      = 0
      max_ttl          = 0
      forwarded_values = {
        headers = []
      }
    },
    {
      path_pattern     = "/app-assets/*"
      target_origin_id = "s3-app-assets"
      compress         = false
      forwarded_values = {
        cookies = {
          forward = "none"
        }
      }
    },
    {
      path_pattern     = "/media-content/*"
      target_origin_id = "traefik-public-v3-dev"
      compress         = false
      forwarded_values = {
        cookies = {
          forward = "none"
        }
      }
    },
    {
      path_pattern     = "ownership/*handpicked*"
      target_origin_id = "s3-shared-cloudfront-404-non-http"
      compress         = false
      forwarded_values = {
        cookies = {
          forward = "none"
        }
      }
      lambda_function_associations = [
        {
          event_type   = "origin-request"
          include_body = false
          lambda_arn   = data.aws_lambda_function.edge["lambda-shared-redirect-handler:${local.active_shared_redirect_handler}"].qualified_arn
        },
      ]
      # response_headers_policy_id = resource.aws_cloudfront_response_headers_policy.static_assets.id
    },
    {
      path_pattern     = "ownership/ebrochures*"
      target_origin_id = "s3-shared-cloudfront-404-non-http"
      compress         = false
      # response_headers_policy_id = resource.aws_cloudfront_response_headers_policy.static_assets.id
    },
    {
      path_pattern     = "/_next/data/caravan-search-*"
      target_origin_id = "traefik-public-v3-dev"
      forwarded_values = {
        headers = [
          "Host",
        ],
        cookies = {
          forward = "none"
        }
      }
    },
    {
      path_pattern     = "/_next/data/caravans-for-sale-*"
      target_origin_id = "traefik-public-v3-dev"
      forwarded_values = {
        headers = [
          "Host",
        ],
        query_string = false
        cookies = {
          forward = "none"
        }
      }
    },
    {
      path_pattern     = "/_next/data/holiday-search-*"
      target_origin_id = "traefik-public-v3-dev"
      forwarded_values = {
        headers = [
          "Host",
          "Referer",
        ],
        cookies = {
          forward = "all"
        }
      }
    },
    {
      path_pattern     = "/_next/*"
      target_origin_id = "traefik-public-v3-dev"
      forwarded_values = {
        query_string = false
        cookies = {
          forward = "none"
        }
      }
    },
    {
      path_pattern     = "/haven-test20/api*"
      target_origin_id = "traefik-public-v3-dev"
      allowed_methods  = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      forwarded_values = {
        cookies = {
          forward = "none"
        }
      }
    },
    {
      path_pattern             = "/caravans-for-sale/caravan-search*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      compress                 = true
      default_ttl              = 0
      max_ttl                  = 0
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
    },
    {
      path_pattern             = "/caravans-for-sale/caravan/*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      compress                 = true
      default_ttl              = 0
      max_ttl                  = 0
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
    },
    {
      path_pattern             = "/caravans-for-sale/api*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      default_ttl              = 0
      max_ttl                  = 0
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
    },
    {
      path_pattern             = "/caravans-for-sale/request-a-brochure"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
      forwarded_values = {
        cookies = {
          forward           = "whitelist"
          whitelisted_names = ["*none"]
        }
      },
      lambda_function_associations = [
        {
          event_type   = "origin-request"
          include_body = false
          lambda_arn   = data.aws_lambda_function.edge["lambda-shared-redirect-handler:${local.active_shared_redirect_handler}"].qualified_arn
        },
      ]
    },
    {
      path_pattern             = "/caravans-for-sale/arrange-a-visit"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
      forwarded_values = {
        cookies = {
          forward           = "whitelist"
          whitelisted_names = ["*none"]
        }
      },
      lambda_function_associations = [
        {
          event_type   = "origin-request"
          include_body = false
          lambda_arn   = data.aws_lambda_function.edge["lambda-shared-redirect-handler:${local.active_shared_redirect_handler}"].qualified_arn
        },
      ]
    },
    {
      path_pattern               = "/caravans-for-sale*"
      target_origin_id           = "traefik-public-v3-dev"
      allowed_methods            = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      cache_policy_id            = aws_cloudfront_cache_policy.ddos_protection.id
      origin_request_policy_id   = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
      response_headers_policy_id = aws_cloudfront_response_headers_policy.ddos_protection.id
      forwarded_values = {
        cookies = {
          forward           = "whitelist"
          whitelisted_names = ["*none"]
        }
      },
      lambda_function_associations = [
        {
          event_type   = "origin-request"
          include_body = false
          lambda_arn   = data.aws_lambda_function.edge["lambda-shared-redirect-handler:${local.active_shared_redirect_handler}"].qualified_arn
        },
      ]
    },
    {
      path_pattern     = "/caravan-search/*"
      target_origin_id = "traefik-public-v3-dev"
      allowed_methods  = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      forwarded_values = {
        query_string = false
        cookies = {
          forward           = "whitelist"
          whitelisted_names = ["*none"]
        }
      }
    },
    {
      path_pattern             = "/caravan-imagery/preview/*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["GET", "HEAD", "OPTIONS"]
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
    },
    {
      path_pattern     = "/haven20/*"
      target_origin_id = "traefik-public-v3-dev"
      forwarded_values = {
        cookies = {
          forward = "none"
        }
      }
    },
    {
      path_pattern     = "/robots.txt"
      target_origin_id = "traefik-public-v3-dev"
      forwarded_values = {
        cookies = {
          forward = "none"
        }
      }
    },
    {
      path_pattern               = "/"
      target_origin_id           = "traefik-public-v3-dev"
      allowed_methods            = ["GET", "HEAD", "OPTIONS"]
      cache_policy_id            = aws_cloudfront_cache_policy.ddos_protection.id
      origin_request_policy_id   = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
      response_headers_policy_id = aws_cloudfront_response_headers_policy.ddos_protection.id
    },
    {
      path_pattern               = "/campaign/*"
      target_origin_id           = "traefik-public-v3-dev"
      allowed_methods            = ["GET", "HEAD", "OPTIONS"]
      cache_policy_id            = aws_cloudfront_cache_policy.ddos_protection.id
      origin_request_policy_id   = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
      response_headers_policy_id = aws_cloudfront_response_headers_policy.ddos_protection.id
    },
    {
      path_pattern     = "/api/barclaycard-payments/*"
      target_origin_id = "traefik-public-v3-dev"
      allowed_methods  = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      default_ttl      = 0
      max_ttl          = 0
      compress         = false
      forwarded_values = {
        headers = [
          "Accept",
          "Authorization",
          "Host",
        ]
        cookies = {
          forward           = "whitelist"
          whitelisted_names = ["bl-sid"]
        }
      }
    },
    {
      path_pattern             = "/api/preview"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
      forwarded_values = {
        headers = [
          "Accept",
          "Authorization",
          "Referer",
          "Host",
        ]
        cookies = {
          forward = "whitelist"
          whitelisted_names = [
            "ASP.NET_SessionId",
            "blg-app-router",
            "x-regions-dc",
          ]
        }
      }
    },
    {
      path_pattern             = "/api/exit-preview"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
    },
    {
      path_pattern             = "/api/cms/*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
      forwarded_values = {
        headers = [
          "Accept",
          "Authorization",
          "Referer",
          "Host",
        ]
        cookies = {
          forward = "whitelist"
          whitelisted_names = [
            "ASP.NET_SessionId",
            "blg-app-router",
            "x-regions-dc",
          ]
        }
      }
    },
    {
      path_pattern             = "/api/blg/*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
      forwarded_values = {
        headers = [
          "Accept",
          "Authorization",
          "Referer",
          "Host",
        ]
        cookies = {
          forward = "whitelist"
          whitelisted_names = [
            "ASP.NET_SessionId",
            "blg-app-router",
            "x-regions-dc",
          ]
        }
      }
    },
    {
      path_pattern             = "/api/ab/*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
      forwarded_values = {
        headers = [
          "Accept",
          "Authorization",
          "Referer",
          "Host",
        ]
      }
    },
    {
      path_pattern             = "/api/search/ab/*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
      forwarded_values = {
        headers = [
          "Accept",
          "Authorization",
          "Referer",
          "Host",
        ]
      }
    },
    {
      path_pattern             = "/holiday-search/_next*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      default_ttl              = 0
      max_ttl                  = 0
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
    },
    {
      path_pattern             = "/one-team-hub*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      default_ttl              = 0
      max_ttl                  = 0
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
    },
    {
      path_pattern             = "/holidays/uk-breaks/weekend/music-weekends/*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["GET", "HEAD", "OPTIONS"]
      default_ttl              = 0
      max_ttl                  = 0
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
      lambda_function_associations = [
        {
          event_type   = "origin-request"
          include_body = false
          lambda_arn   = data.aws_lambda_function.edge["lambda-shared-redirect-handler:${local.active_shared_redirect_handler}"].qualified_arn
        },
      ]
    },
    {
      path_pattern             = "/holidays/curated/*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      default_ttl              = 0
      max_ttl                  = 0
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
    },
    {
      path_pattern               = "/holidays*"
      target_origin_id           = "traefik-public-v3-dev"
      allowed_methods            = ["GET", "HEAD", "OPTIONS"]
      cache_policy_id            = aws_cloudfront_cache_policy.ddos_protection.id
      origin_request_policy_id   = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
      response_headers_policy_id = aws_cloudfront_response_headers_policy.ddos_protection.id
      lambda_function_associations = [
        {
          event_type   = "viewer-request"
          include_body = false
          lambda_arn   = data.aws_lambda_function.edge["lambda-shared-redirect-handler:${local.active_shared_redirect_handler}"].qualified_arn
        },
      ]
    },
    {
      path_pattern               = "/parks*"
      target_origin_id           = "traefik-public-v3-dev"
      allowed_methods            = ["GET", "HEAD", "OPTIONS"]
      cache_policy_id            = aws_cloudfront_cache_policy.ddos_protection.id
      origin_request_policy_id   = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
      response_headers_policy_id = aws_cloudfront_response_headers_policy.ddos_protection.id
      lambda_function_associations = [
        {
          event_type   = "viewer-request"
          include_body = false
          lambda_arn   = data.aws_lambda_function.edge["lambda-shared-redirect-handler:${local.active_shared_redirect_handler}"].qualified_arn
        },
      ]
    },
    {
      path_pattern               = "/partners*"
      target_origin_id           = "traefik-public-v3-dev"
      allowed_methods            = ["GET", "HEAD", "OPTIONS"]
      cache_policy_id            = aws_cloudfront_cache_policy.ddos_protection.id
      origin_request_policy_id   = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
      response_headers_policy_id = aws_cloudfront_response_headers_policy.ddos_protection.id
      lambda_function_associations = [
        {
          event_type   = "viewer-request"
          include_body = false
          lambda_arn   = data.aws_lambda_function.edge["lambda-shared-redirect-handler:${local.active_shared_redirect_handler}"].qualified_arn
        },
      ]
    },
    {
      path_pattern               = "/discover*"
      target_origin_id           = "traefik-public-v3-dev"
      allowed_methods            = ["GET", "HEAD", "OPTIONS"]
      cache_policy_id            = aws_cloudfront_cache_policy.ddos_protection.id
      origin_request_policy_id   = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
      response_headers_policy_id = aws_cloudfront_response_headers_policy.ddos_protection.id
      lambda_function_associations = [
        {
          event_type   = "origin-request"
          include_body = false
          lambda_arn   = data.aws_lambda_function.edge["lambda-shared-redirect-handler:${local.active_shared_redirect_handler}"].qualified_arn
        },
      ]
    },
    {
      path_pattern               = "/about-us*"
      target_origin_id           = "traefik-public-v3-dev"
      allowed_methods            = ["GET", "HEAD", "OPTIONS"]
      cache_policy_id            = aws_cloudfront_cache_policy.ddos_protection.id
      origin_request_policy_id   = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
      response_headers_policy_id = aws_cloudfront_response_headers_policy.ddos_protection.id
      lambda_function_associations = [
        {
          event_type   = "origin-request"
          include_body = false
          lambda_arn   = data.aws_lambda_function.edge["lambda-shared-redirect-handler:${local.active_shared_redirect_handler}"].qualified_arn
        },
      ]
    },
    {
      path_pattern               = "/support*"
      target_origin_id           = "traefik-public-v3-dev"
      allowed_methods            = ["GET", "HEAD", "OPTIONS"]
      cache_policy_id            = aws_cloudfront_cache_policy.ddos_protection.id
      origin_request_policy_id   = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
      response_headers_policy_id = aws_cloudfront_response_headers_policy.ddos_protection.id
      lambda_function_associations = [
        {
          event_type   = "origin-request"
          include_body = false
          lambda_arn   = data.aws_lambda_function.edge["lambda-shared-redirect-handler:${local.active_shared_redirect_handler}"].qualified_arn
        },
      ]
    },

    {
      path_pattern     = "/legacy-imagelibrary/*"
      target_origin_id = "imagelibrary.haven.com"
      forwarded_values = {
        headers = [
          "Host",
        ]
        cookies = {
          forward = "none"
        }
      }
    },
    {
      path_pattern             = "/holiday-inspiration*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["GET", "HEAD", "OPTIONS"]
      default_ttl              = 0
      max_ttl                  = 0
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
      lambda_function_associations = [
        {
          event_type   = "origin-request"
          include_body = false
          lambda_arn   = data.aws_lambda_function.edge["lambda-shared-redirect-handler:${local.active_shared_redirect_handler}"].qualified_arn
        },
      ]
    },
    {
      path_pattern               = "/touring-camping*"
      target_origin_id           = "traefik-public-v3-dev"
      allowed_methods            = ["GET", "HEAD", "OPTIONS"]
      cache_policy_id            = aws_cloudfront_cache_policy.ddos_protection.id
      origin_request_policy_id   = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
      response_headers_policy_id = aws_cloudfront_response_headers_policy.ddos_protection.id
      lambda_function_associations = [
        {
          event_type   = "origin-request"
          include_body = false
          lambda_arn   = data.aws_lambda_function.edge["lambda-shared-redirect-handler:${local.active_shared_redirect_handler}"].qualified_arn
        },
      ]
    },
    {
      path_pattern               = "/sitemap-2.0.xml"
      target_origin_id           = "traefik-public-v3-dev"
      allowed_methods            = ["GET", "HEAD", "OPTIONS"]
      cache_policy_id            = aws_cloudfront_cache_policy.ddos_protection.id
      origin_request_policy_id   = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
      response_headers_policy_id = aws_cloudfront_response_headers_policy.ddos_protection.id
    },
    {
      path_pattern     = "/search-results/holidays*"
      target_origin_id = "traefik-public-v3-dev"
      allowed_methods  = ["GET", "HEAD", "OPTIONS"]
      compress         = false
      default_ttl      = 0
      max_ttl          = 0
      forwarded_values = {
        headers = [
          "Host",
        ]
      }
    },
    {
      path_pattern             = "/search-results/caravan-search*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      compress                 = true
      default_ttl              = 0
      max_ttl                  = 0
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
    },
    {
      path_pattern     = "/search-results/caravans*"
      target_origin_id = "traefik-public-v3-dev"
      allowed_methods  = ["GET", "HEAD", "OPTIONS"]
      compress         = false
      default_ttl      = 0
      max_ttl          = 0
      forwarded_values = {
        headers = [
          "Host",
        ]
      }
    },
    {
      path_pattern     = "/search-results/caravans/saved*"
      target_origin_id = "traefik-public-v3-dev"
      allowed_methods  = ["GET", "HEAD", "OPTIONS"]
      compress         = false
      default_ttl      = 0
      max_ttl          = 0
      forwarded_values = {
        headers = [
          "Host",
        ]
      }
    },
    {
      path_pattern             = "/search-results/touring*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      compress                 = true
      default_ttl              = 0
      max_ttl                  = 0
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
    },
    {
      path_pattern             = "/search-results/parks*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      compress                 = true
      default_ttl              = 0
      max_ttl                  = 0
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
    },
    {
      path_pattern             = "/search-results/grades*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      compress                 = true
      default_ttl              = 0
      max_ttl                  = 0
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
    },
    {
      path_pattern             = "/search-results/dates*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      compress                 = true
      default_ttl              = 0
      max_ttl                  = 0
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
    },
    {
      path_pattern             = "/search-results/bedrooms*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      compress                 = true
      default_ttl              = 0
      max_ttl                  = 0
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
    },
    {
      path_pattern             = "/search-results/packages*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      compress                 = true
      default_ttl              = 0
      max_ttl                  = 0
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
    },
    {
      path_pattern             = "/search-results/saved*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      compress                 = true
      default_ttl              = 0
      max_ttl                  = 0
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
    },
    {
      path_pattern             = "/build-your-break*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      compress                 = true
      default_ttl              = 0
      max_ttl                  = 0
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
    },
    {
      path_pattern             = "/search-results/break-builder*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      compress                 = true
      default_ttl              = 0
      max_ttl                  = 0
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
    },
    {
      path_pattern             = "/map*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      compress                 = true
      default_ttl              = 0
      max_ttl                  = 0
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
    },
    {
      path_pattern     = "/search-results/_next*"
      target_origin_id = "traefik-public-v3-dev"
      compress         = false
      forwarded_values = {
        headers = [
          "Host",
        ]
      }
    },
    {
      path_pattern             = "/search-results/v2/api*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      compress                 = true
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
    },
    {
      path_pattern             = "/search-results/v4/api*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      compress                 = true
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
    },
    {
      path_pattern             = "/postcode-address/api/v1*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      compress                 = true
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
    },
    {
      path_pattern             = "/entertainment*" # deprecated - see /experience/entertainment*
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["GET", "HEAD", "OPTIONS"]
      compress                 = false
      efault_ttl               = 0
      max_ttl                  = 0
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
    },
    {
      path_pattern             = "/order*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["GET", "HEAD", "OPTIONS", "DELETE", "PATCH", "POST", "PUT"]
      compress                 = false
      default_ttl              = 0
      max_ttl                  = 0
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
    },
    {
      path_pattern             = "/qr*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["GET", "HEAD"]
      compress                 = false
      default_ttl              = 0
      max_ttl                  = 0
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
    },
    {
      path_pattern             = "/arrivals*" # deprecated - see /experience/my-holiday*
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["GET", "HEAD", "OPTIONS", "DELETE", "PATCH", "POST", "PUT"]
      compress                 = false
      default_ttl              = 0
      max_ttl                  = 0
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
    },
    {
      path_pattern             = "/experience"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["GET", "HEAD", "OPTIONS", "DELETE", "PATCH", "POST", "PUT"]
      compress                 = false
      default_ttl              = 0
      max_ttl                  = 0
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
    },
    {
      path_pattern             = "/experience/api*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["GET", "HEAD", "OPTIONS", "DELETE", "PATCH", "POST", "PUT"]
      compress                 = false
      default_ttl              = 0
      max_ttl                  = 0
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
    },
    {
      path_pattern             = "/experience/identity*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      compress                 = false
      default_ttl              = 0
      max_ttl                  = 0
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
    },
    {
      path_pattern       = "/play-pass*"
      target_origin_id   = "traefik-public-v3-dev"
      synthetic_test_url = "/play-pass*"
      allowed_methods    = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      forwarded_values = {
        cookies = {
          forward           = "whitelist"
          whitelisted_names = ["bl-sid", "PLAYPASS_LETMEIN"]
        }
      }
    },
    {
      path_pattern       = "/api/play-pass/*"
      target_origin_id   = "traefik-public-v3-dev"
      synthetic_test_url = "/api/play-pass/*"
      allowed_methods    = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      default_ttl        = 0
      max_ttl            = 0
      compress           = false
      forwarded_values = {
        headers = [
          "Accept",
          "Authorization",
          "Host",
        ]
        cookies = {
          forward           = "whitelist"
          whitelisted_names = ["bl-sid"]
        }
      }
    },
    {
      path_pattern             = "/experience/my-holiday*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["GET", "HEAD", "OPTIONS", "DELETE", "PATCH", "POST", "PUT"]
      compress                 = false
      default_ttl              = 0
      max_ttl                  = 0
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
    },
    {
      path_pattern             = "/experience/entertainment*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["HEAD", "DELETE", "POST", "GET", "OPTIONS", "PUT", "PATCH"]
      compress                 = false
      default_ttl              = 0
      max_ttl                  = 0
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
    },
    {
      path_pattern             = "/experience/food/api*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["GET", "HEAD", "OPTIONS", "DELETE", "PATCH", "POST", "PUT"]
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
      forwarded_values = {
        headers = ["Host", "Authorization", "x-api-key"]
        cookies = {
          forward = "whitelist"
          whitelisted_names = [
            "bl-sid"
          ]
        }
      }
    },
    {
      path_pattern             = "/experience/food*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["GET", "HEAD", "OPTIONS"]
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
    },
    {
      path_pattern             = "/experience/activities"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
    },
    {
      path_pattern             = "/team-app/api*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
    },
    {
      path_pattern             = "/wifi/api*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      compress                 = false
      default_ttl              = 0
      max_ttl                  = 0
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
    },
    {
      path_pattern             = "/experience/activities/*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
    },
    {
      path_pattern             = "/experience/public-activity-list*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["GET", "HEAD", "OPTIONS"]
      compress                 = false
      default_ttl              = 0
      max_ttl                  = 0
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
    },
    {
      path_pattern             = "/experience/*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["GET", "HEAD", "OPTIONS", "DELETE", "PATCH", "POST", "PUT"]
      compress                 = false
      default_ttl              = 0
      max_ttl                  = 0
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
    },
    {
      path_pattern             = "/public-activity-list*" # deprecated - see /experience/public-activity-list*
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["GET", "HEAD", "OPTIONS"]
      compress                 = false
      default_ttl              = 0
      max_ttl                  = 0
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
    },
    {
      path_pattern             = "/food/api*"
      target_origin_id         = "traefik-public-v3-dev"
      compress                 = false
      default_ttl              = 0
      max_ttl                  = 0
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
      allowed_methods          = ["GET", "HEAD", "OPTIONS", "POST", "PUT", "DELETE", "PATCH"]
      forwarded_values = {
        cookies = {
          forward = "whitelist"
          whitelisted_names = [
            "bl-sid"
          ]
        }
      }
    },
    {
      path_pattern     = "/food*" # deprecated - see /experience/food*
      target_origin_id = "traefik-public-v3-dev"
      allowed_methods  = ["GET", "HEAD", "OPTIONS"]
      forwarded_values = {
        cookies = {
          forward = "whitelist"
          whitelisted_names = [
            "bl-sid"
          ]
        }
      }
    },
    {
      path_pattern           = "/registeryourstay*"
      target_origin_id       = "S3-devprod.haven.com"
      compress               = false
      viewer_protocol_policy = "allow-all"
      forwarded_values = {
        headers      = []
        query_string = false
        cookies = {
          forward = "none"
        }
      }
    },
    {
      path_pattern           = "/register"
      target_origin_id       = "S3-devprod.haven.com"
      allowed_methods        = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      default_ttl            = 0
      max_ttl                = 0
      viewer_protocol_policy = "allow-all"
      forwarded_values = {
        headers      = []
        query_string = false
        cookies = {
          forward = "none"
        }
      }
    },
    {
      path_pattern             = "/booking/_next*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      default_ttl              = 0
      max_ttl                  = 0
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_optimized.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
    },
    {
      path_pattern             = "/booking/api/*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      default_ttl              = 0
      max_ttl                  = 0
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
      forwarded_values = {
        headers = [
          "Accept",
          "Authorization",
          "Host",
        ]
      }
    },
    {
      path_pattern     = "/booking*"
      target_origin_id = "traefik-public-v3-dev"
      allowed_methods  = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      default_ttl      = 0
      max_ttl          = 0
      forwarded_values = {
        headers = [
          "Accept",
          "Authorization",
          "Host",
        ]
      }
    },

    {
      path_pattern     = "/my-account/_next*"
      target_origin_id = "traefik-public-v3-dev"
      allowed_methods  = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
    },
    {
      path_pattern             = "/my-account/api*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      default_ttl              = 0
      max_ttl                  = 0
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
      forwarded_values = {
        headers = [
          "Accept",
          "Authorization",
          "Host",
        ]
      }
    },
    {
      path_pattern             = "/session/api/*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      default_ttl              = 0
      max_ttl                  = 0
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
      forwarded_values = {
        headers = [
          "Accept",
          "Authorization",
          "Host",
        ]
      }
    },
    {
      path_pattern             = "/affiliate/api/*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      default_ttl              = 0
      max_ttl                  = 0
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
      forwarded_values = {
        headers = [
          "Accept",
          "Authorization",
          "Host",
        ]
      }
    },
    {
      path_pattern               = "/newsletter*"
      target_origin_id           = "traefik-public-v3-dev"
      allowed_methods            = ["GET", "HEAD", "OPTIONS"]
      cache_policy_id            = aws_cloudfront_cache_policy.ddos_protection.id
      origin_request_policy_id   = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
      response_headers_policy_id = aws_cloudfront_response_headers_policy.ddos_protection.id
      lambda_function_associations = [
        {
          event_type   = "origin-request"
          include_body = false
          lambda_arn   = data.aws_lambda_function.edge["lambda-shared-redirect-handler:${local.active_shared_redirect_handler}"].qualified_arn
        },
      ]
    },
    {
      path_pattern             = "/my-account"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      default_ttl              = 0
      max_ttl                  = 0
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
      forwarded_values = {
        headers = [
          "Accept",
          "Authorization",
          "Host",
        ]
      }
    },
    {
      path_pattern             = "/my-account/*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      default_ttl              = 0
      max_ttl                  = 0
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
      forwarded_values = {
        headers = [
          "Accept",
          "Authorization",
          "Host",
        ]
      }
    },
    {
      path_pattern               = "/beaches*"
      target_origin_id           = "traefik-public-v3-dev"
      allowed_methods            = ["GET", "HEAD", "OPTIONS"]
      cache_policy_id            = aws_cloudfront_cache_policy.ddos_protection.id
      origin_request_policy_id   = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
      response_headers_policy_id = aws_cloudfront_response_headers_policy.ddos_protection.id
      lambda_function_associations = [
        {
          event_type   = "origin-request"
          include_body = false
          lambda_arn   = data.aws_lambda_function.edge["lambda-shared-redirect-handler:${local.active_shared_redirect_handler}"].qualified_arn
        },
      ]
    },
    {
      path_pattern               = "/things-to-do*"
      target_origin_id           = "traefik-public-v3-dev"
      allowed_methods            = ["GET", "HEAD", "OPTIONS"]
      cache_policy_id            = aws_cloudfront_cache_policy.ddos_protection.id
      origin_request_policy_id   = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
      response_headers_policy_id = aws_cloudfront_response_headers_policy.ddos_protection.id

      lambda_function_associations = [
        {
          event_type   = "origin-request"
          include_body = false
          lambda_arn   = data.aws_lambda_function.edge["lambda-shared-redirect-handler:${local.active_shared_redirect_handler}"].qualified_arn
        },
      ]

    },
    {
      path_pattern     = "/assets/caravan-images/*"
      target_origin_id = "s3-holidaysales-shared-caravan-imagery"
      allowed_methods  = ["GET", "HEAD", "OPTIONS"]
      cache_policy_id  = data.aws_cloudfront_cache_policy.managed_caching_optimized.id
    },
    {
      path_pattern     = "/assets/*"
      target_origin_id = "s3-shared-cloudfront-static-assets"
      allowed_methods  = ["GET", "HEAD", "OPTIONS"]
      default_ttl      = 0
      max_ttl          = 0
      cache_policy_id  = resource.aws_cloudfront_cache_policy.custom_origin_caching_seven_days.id
    },
    {
      path_pattern     = "/favicon.ico"
      target_origin_id = "s3-shared-cloudfront-static-assets"
      allowed_methods  = ["GET", "HEAD", "OPTIONS"]
      default_ttl      = 0
      max_ttl          = 0
      cache_policy_id  = resource.aws_cloudfront_cache_policy.custom_origin_caching_seven_days.id
    },
    {
      path_pattern               = "/offers*"
      target_origin_id           = "traefik-public-v3-dev"
      allowed_methods            = ["GET", "HEAD", "OPTIONS"]
      compress                   = false
      cache_policy_id            = aws_cloudfront_cache_policy.ddos_protection.id
      origin_request_policy_id   = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
      response_headers_policy_id = aws_cloudfront_response_headers_policy.ddos_protection.id

      lambda_function_associations = [
        {
          event_type   = "origin-request"
          include_body = false
          lambda_arn   = data.aws_lambda_function.edge["lambda-shared-redirect-handler:${local.active_shared_redirect_handler}"].qualified_arn
        },
      ]
    },
    {
      path_pattern           = "/.well-known/*"
      target_origin_id       = "s3-shared-cloudfront-static-assets"
      default_ttl            = 0
      max_ttl                = 0
      viewer_protocol_policy = "allow-all"
      cache_policy_id        = data.aws_cloudfront_cache_policy.managed_caching_optimized.id
    },
    {
      path_pattern     = "/google7d9c66aec4fee38f.html"
      target_origin_id = "s3-shared-cloudfront-static-assets"
      default_ttl      = 0
      max_ttl          = 0
      cache_policy_id  = data.aws_cloudfront_cache_policy.managed_caching_optimized.id
    },
    {
      path_pattern     = "/cyhh/_next/*"
      target_origin_id = "traefik-public-v3-dev"
      allowed_methods  = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
    },
    {
      path_pattern             = "/cyhh/api/*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      default_ttl              = 0
      max_ttl                  = 0
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
      forwarded_values = {
        headers = [
          "Accept",
          "Authorization",
          "Host",
        ]
      }
    },
    {
      path_pattern             = "/cyhh"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      default_ttl              = 0
      max_ttl                  = 0
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
      forwarded_values = {
        headers = [
          "Accept",
          "Authorization",
          "Host",
        ]
      }
    },
    {
      path_pattern             = "/cyhh/*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      default_ttl              = 0
      max_ttl                  = 0
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
      forwarded_values = {
        headers = [
          "Accept",
          "Authorization",
          "Host",
        ]
      }
    },
    {
      path_pattern             = "/contact-preferences*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      default_ttl              = 0
      max_ttl                  = 0
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
      forwarded_values = {
        headers = [
          "Accept",
          "Authorization",
          "Host",
        ]
      }
    },
    {
      path_pattern             = "/gift-card*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      default_ttl              = 0
      max_ttl                  = 0
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
    },
    {
      path_pattern             = "/prospect-questionnaire*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      default_ttl              = 0
      max_ttl                  = 0
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
    },
    { # used to verify the x-forward-for header behaviour for configuration of traefik rate limiting per IP address
      path_pattern             = "/sre-echo*"
      target_origin_id         = "traefik-public-v3-dev"
      allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      default_ttl              = 0
      max_ttl                  = 0
      cache_policy_id          = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      origin_request_policy_id = data.aws_cloudfront_origin_request_policy.managed_all_viewer.id
    },
    // All behaviours above this line will be available even when the holding page is active (i.e. path_pattern = "*")
    {
      path_pattern     = "/holding-page"
      target_origin_id = "s3-shared-cloudfront-static-holding-page-non-http"
      allowed_methods  = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      default_ttl      = 0
      max_ttl          = 0
      cache_policy_id  = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      # response_headers_policy_id = resource.aws_cloudfront_response_headers_policy.static_assets.id
    },
    // Put this path higher than the `/holding-page` path if you want the holding page with no phone number on
    {
      path_pattern     = "/alternate-holding-page"
      target_origin_id = "s3-shared-cloudfront-static-alternate-holding-page-non-http"
      allowed_methods  = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
      default_ttl      = 0
      max_ttl          = 0
      cache_policy_id  = data.aws_cloudfront_cache_policy.managed_caching_disabled.id
      # response_headers_policy_id = resource.aws_cloudfront_response_headers_policy.static_assets.id
    },
    // All behaviours below this line will be routed to the holding page when it is active (i.e. path_pattern = "*")
  ]
}
