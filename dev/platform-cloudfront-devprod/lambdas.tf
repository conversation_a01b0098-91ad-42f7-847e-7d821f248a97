locals {
  /*
   * Declare AWS Lambda functions to be defined as datasources so that we can
   * reference their ARN in Cloudfront behavior config. Each object in the list
   * defines a function name and a list of its versions that are in use.
   *
   * In order to aid with keeping this local variable tidy, there is a script
   * in this module (`validate-lambdas`) which will print the identifier for
   * lambda functions that are declared in the state but not in use in the
   * config.
   *
   */
  active_shared_redirect_handler = "167"
  edge_lambdas = [
    {
      name     = "lambda-shared-ab-testing"
      versions = ["29", "30", "32"]
    },
    {
      name     = "lambda-shared-origin-response"
      versions = ["6", "8"]
    },
    {
      name     = "lambda-shared-redirect-handler"
      versions = ["167"]
    }
  ]
}

/*
 * Based on the entries above, this datasource allows referencing AWS Lambda
 * functions in AWS Cloudfront behavior configuration like so:
 *
 *   data.aws_lambda_function.edge["<function-name>:<function-version>"]
 *
 */
data "aws_lambda_function" "edge" {
  provider = aws.us-east-1

  /*
   * Loops through all entries in the local variable above and constructs all
   * combinations of AWS Lambda function name and version (qualifier).
   *
   */
  for_each = merge([for l in local.edge_lambdas : {
    for p in setproduct([l.name], l.versions) :
    join(":", p) => { "name" : p[0], "version" : p[1] }
  }]...)

  function_name = each.value.name
  qualifier     = each.value.version
}
