data "terraform_remote_state" "foundation_event_generation" {
  backend = "s3"

  config = {
    bucket  = var.terraform_state_bucket
    key     = "dev/foundation-event-generation"
    profile = var.terraform_state_profile
    region  = var.terraform_state_region
  }
}

# This references resources that have been deleted, but we should be re-adding these in the future, so leaving here
# module "sqs_queue_service_haven_table_booking_holiday_cancellation" {
#   source = "**************:HavenEngineering/tf-sqs-queue?ref=0.9.0"

#   meta = local.meta

#   consumer_name              = "service-haven-table-booking"
#   consumer_purpose           = "delete"
#   message_type               = "holiday-cancellation"
#   sns_topic_arns             = [data.terraform_remote_state.foundation_event_generation.outputs.holiday_bookings_sns_arn]
#   filter_policy              = "{ \"entity\": [\"HOLIDAY_BOOKING\"], \"messageType\": [\"HOLIDAY_BOOKING_STATUS_CHANGED\"], \"body\": { \"oldBookingDetails\": { \"bookingStatus\": [\"BOOKED\", \"ARRIVAL_CONFIRMED\"] }, \"newBookingDetails\": { \"bookingStatus\": [\"CANCELED\"] } } }"
#   filter_policy_scope        = "MessageBody"
#   visibility_timeout_seconds = 30
# }
