data "aws_security_group" "dms_sg" {
  vpc_id = data.aws_vpc.main.id

  filter {
    name   = "group-name"
    values = ["dev-experience-shared-dms"]
  }
}

module "postgres_service_haven_entertainment" {
  source        = "**************:HavenEngineering/tf-rds-instance?ref=v4.2.1"
  resource_name = "dev-experience-entertainment"

  # add replication

  parameters = [
    {
      name  = "rds.logical_replication",
      value = "1",
    },
    {
      name  = "rds.force_ssl",
      value = "0",
  }]

  # tag for dashboards

  additional_instance_tags = {
    "blg:dms" : "experience"
    "StartInstance" : base64encode("cron(0 2 ? * WED *)|cron(50 5 ? * MON-FRI *)")
    "StopInstance" : base64encode("cron(0 3 ? * WED *)|cron(05 20 ? * MON-FRI *)")
    "StopOverride" : "dd.mm.yyyy"
  }

  # emergency auto-scaling to provide a buffer

  max_allocated_storage = 1000

  instance_type                 = "db.t4g.small"
  parameters_family             = "postgres15"
  aws_db_parameter_group_suffix = "-v15"
  engine_version                = "15.12"
  # grant access for the replication instance to communicate with the source database
  security_group_ids = [data.aws_security_group.dms_replication_instances.id]
  source_cidr_blocks = local.dev_db_default_allowed_cidrs
  multi_az           = false

  cloudwatch_actions = {
    warning  = data.aws_sns_topic.rds_alerts.arn
    critical = data.aws_sns_topic.rds_alerts.arn
  }
}

module "postgres_service_haven_table_booking" {
  source        = "**************:HavenEngineering/tf-rds-instance?ref=v4.2.0"
  resource_name = "dev-experience-table-booking"

  # add replication

  parameters = [
    {
      name  = "rds.logical_replication",
      value = "0",
    },
    {
      name  = "rds.force_ssl",
      value = "0",
  }]

  # tag for dashboards

  additional_instance_tags = {
    "blg:dms" : "experience"
    "StartInstance" : base64encode("cron(0 2 ? * WED *)|cron(50 5 ? * MON-FRI *)")
    "StopInstance" : base64encode("cron(0 3 ? * WED *)|cron(05 20 ? * MON-FRI *)")
    "StopOverride" : "dd.mm.yyyy"
  }

  # emergency auto-scaling to provide a buffer

  max_allocated_storage = 1000

  instance_type                 = "db.t4g.small"
  parameters_family             = "postgres15"
  aws_db_parameter_group_suffix = "-v15"
  engine_version                = "15.12"
  # grant access for the replication instance to communicate with the source database
  source_cidr_blocks = local.dev_db_default_allowed_cidrs
  multi_az           = false

  cloudwatch_actions = {
    warning  = data.aws_sns_topic.rds_alerts.arn
    critical = data.aws_sns_topic.rds_alerts.arn
  }
}

module "postgres_service_haven_experience_admin_auth" {
  source        = "**************:HavenEngineering/tf-rds-instance?ref=v4.2.1"
  resource_name = "dev-experience-experience-admin-auth"

  instance_type                 = "db.t4g.small"
  parameters_family             = "postgres15"
  aws_db_parameter_group_suffix = "-v15"
  engine_version                = "15.12"
  security_group_ids            = [data.aws_security_group.dms_replication_instances.id]
  source_cidr_blocks            = local.dev_db_default_allowed_cidrs
  multi_az                      = false

  cloudwatch_actions = {
    warning  = data.aws_sns_topic.rds_alerts.arn
    critical = data.aws_sns_topic.rds_alerts.arn
  }


  parameters = [
    {
      name  = "rds.logical_replication",
      value = "1",
    },
    {
      name  = "rds.force_ssl",
      value = "0",
  }]

  additional_instance_tags = {
    "blg:dms" : "experience"
    "StartInstance" : base64encode("cron(0 2 ? * WED *)|cron(50 5 ? * MON-FRI *)")
    "StopInstance" : base64encode("cron(0 3 ? * WED *)|cron(05 20 ? * MON-FRI *)")
    "StopOverride" : "dd.mm.yyyy"
  }
}

module "postgres_app_haven_experience_admin_unleash" {
  source        = "**************:HavenEngineering/tf-rds-instance?ref=v4.2.0"
  resource_name = "dev-experience-experience-admin-unleash"


  instance_type                 = "db.t4g.small"
  parameters_family             = "postgres15"
  aws_db_parameter_group_suffix = "-v15"
  engine_version                = "15.12"
  source_cidr_blocks            = local.dev_db_default_allowed_cidrs
  multi_az                      = false

  parameters = [
    {
      name  = "rds.logical_replication",
      value = "1",
    },
    {
      name  = "rds.force_ssl",
      value = "0",
  }]

  cloudwatch_actions = {
    warning  = data.aws_sns_topic.rds_alerts.arn
    critical = data.aws_sns_topic.rds_alerts.arn
  }

  additional_instance_tags = {
    "StartInstance" : base64encode("cron(0 2 ? * WED *)|cron(50 5 ? * MON-FRI *)")
    "StopInstance" : base64encode("cron(0 3 ? * WED *)|cron(05 20 ? * MON-FRI *)")
    "StopOverride" : "dd.mm.yyyy"
  }
}
