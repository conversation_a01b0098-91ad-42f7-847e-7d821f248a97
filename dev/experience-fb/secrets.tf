data "terraform_remote_state" "shared_playpass" {
  backend = "s3"

  config = {
    bucket  = var.terraform_state_bucket
    key     = "dev/shared-playpass"
    profile = var.terraform_state_profile
    region  = var.terraform_state_region
  }
}

data "terraform_remote_state" "experience_activities" {
  backend = "s3"

  config = {
    bucket  = var.terraform_state_bucket
    key     = "dev/experience-activities"
    profile = var.terraform_state_profile
    region  = var.terraform_state_region
  }
}

data "terraform_remote_state" "experience-aac" {
  backend = "s3"

  config = {
    bucket  = var.terraform_state_bucket
    key     = "dev/experience-aac"
    profile = var.terraform_state_profile
    region  = var.terraform_state_region
  }
}

locals {
  shared_playpass_api_keys = data.terraform_remote_state.shared_playpass.outputs.api_keys
  sid_auth_secret          = data.terraform_remote_state.experience_activities.outputs.sid_auth_secret
  venue_disruption_api_key = data.terraform_remote_state.experience-aac.outputs.venue_disruption_api_key
}

module "secrets_service_haven_table_booking_new" {
  source               = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"
  kubernetes_name      = "service-haven-table-booking"
  kubernetes_namespace = "food-and-beverage"

  secrets = {
    DB_USER_NAME        = module.postgres_service_haven_table_booking.db_username
    DB_USER_PASSWORD    = module.postgres_service_haven_table_booking.db_password
    DB_HOST             = module.postgres_service_haven_table_booking.db_address
    DB_NAME             = module.postgres_service_haven_table_booking.db_name
    ZONAL_API_KEY       = trimspace(file("${path.module}/secrets-dev/secrets/api-key-for-zonal-client.secret"))
    EVENTS_API_USERNAME = trimspace(file("${path.module}/secrets-dev/secrets/events-api-username.secret"))
    EVENTS_API_PASSWORD = trimspace(file("${path.module}/secrets-dev/secrets/events-api-password.secret"))
    IORDER_API_KEY      = trimspace(file("${path.module}/secrets-dev/secrets/iorder-api-key.secret"))
    # These reference resources that have been deleted, but we should be re-adding these in the future, so leaving here
    # HOLIDAY_BOOKING_CANCELLATION_QUEUE_URL            = module.sqs_queue_service_haven_table_booking_holiday_cancellation.work_queue_url
    # HOLIDAY_BOOKING_CANCELLATION_QUEUE_DEADLETTER_URL = module.sqs_queue_service_haven_table_booking_holiday_cancellation.deadletter_queue_url
  }
}

# service-haven-experience-admin-auth

module "secrets_service_haven_experience_admin_auth_new" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "service-haven-experience-admin-auth"
  kubernetes_namespace = "eat" # note that this has no relevance to actual k8s, it is just used to create the secret pathname

  secrets = {
    DB_USER_NAME     = module.postgres_service_haven_experience_admin_auth.db_username
    DB_USER_PASSWORD = module.postgres_service_haven_experience_admin_auth.db_password
    DB_HOST          = module.postgres_service_haven_experience_admin_auth.db_address
    DB_NAME          = module.postgres_service_haven_experience_admin_auth.db_name
  }
}

# service-haven-entertainment

module "secrets_service_haven_entertainment_new" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "service-haven-entertainment"
  kubernetes_namespace = "entertainment" # note that this has no relevance to actual k8s, it is just used to create the secret pathname

  secrets = {
    DB_USER_NAME     = module.postgres_service_haven_entertainment.db_username
    DB_USER_PASSWORD = module.postgres_service_haven_entertainment.db_password
    DB_HOST          = module.postgres_service_haven_entertainment.db_address
    DB_NAME          = module.postgres_service_haven_entertainment.db_name
  }
}

# app-haven-experience-admin hosting application

module "secrets_app_haven_experience_admin_new" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "app-haven-experience-admin"
  kubernetes_namespace = "eat" # note that this has no relevance to actual k8s, it is just used to create the secret pathname

  secrets = {
    VITE_CONTENTFUL_PREVIEW_API_ACCESS_TOKEN  = trimspace(file("${path.module}/../common-secrets/secrets/contentful/contentful-api-preview-experience-dev.secret"))
    VITE_CONTENTFUL_DELIVERY_API_ACCESS_TOKEN = trimspace(file("${path.module}/../common-secrets/secrets/contentful/contentful-api-delivery-experience-dev.secret"))
    DB_USER_NAME                              = module.postgres_app_haven_experience_admin_unleash.db_username
    DB_USER_PASSWORD                          = module.postgres_app_haven_experience_admin_unleash.db_password
    DB_HOST                                   = module.postgres_app_haven_experience_admin_unleash.db_address
    DB_NAME                                   = module.postgres_app_haven_experience_admin_unleash.db_name
  }
}

# service-haven-experience-admin-playpass-gateway

module "secrets_service_haven_experience_admin_playpass_gateway_new" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "service-haven-experience-admin-playpass-gateway"
  kubernetes_namespace = "eat" # note that this has no relevance to actual k8s, it is just used to create the secret pathname

  secrets = {
    PLAYPASS_API_KEY = local.shared_playpass_api_keys["service-haven-experience-admin-playpass-gateway"]
  }
}

module "secrets_app_haven_table_booking_new" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "app-haven-table-booking"
  kubernetes_namespace = "food-and-beverage" # note that this has no relevance to actual k8s, it is just used to create the secret pathname

  secrets = {
    CONTENTFUL_DELIVERY_API_ACCESS_TOKEN = trimspace(file("${path.module}/../common-secrets/secrets/contentful/contentful-api-delivery-experience-dev.secret"))
    CONTENTFUL_PREVIEW_API_ACCESS_TOKEN  = trimspace(file("${path.module}/../common-secrets/secrets/contentful/contentful-api-preview-experience-dev.secret"))
    SID_AUTH_SECRET                      = local.sid_auth_secret
    VENUE_DISRUPTION_AUTH_KEY            = local.venue_disruption_api_key
  }
}

module "secrets_app_haven_entertainment_new" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "app-haven-entertainment"
  kubernetes_namespace = "entertainment" # note that this has no relevance to actual k8s, it is just used to create the secret pathname

  secrets = {
    CONTENTFUL_DELIVERY_API_ACCESS_TOKEN = trimspace(file("${path.module}/../common-secrets/secrets/contentful/contentful-api-delivery-experience-dev.secret"))
    CONTENTFUL_PREVIEW_API_ACCESS_TOKEN  = trimspace(file("${path.module}/../common-secrets/secrets/contentful/contentful-api-preview-experience-dev.secret"))
    SID_AUTH_SECRET                      = local.sid_auth_secret
  }
}

module "secrets_app_haven_order_new" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "app-haven-order"
  kubernetes_namespace = "food-and-beverage" # note that this has no relevance to actual k8s, it is just used to create the secret pathname

  secrets = {
    CONTENTFUL_DELIVERY_API_ACCESS_TOKEN = trimspace(file("${path.module}/../common-secrets/secrets/contentful/contentful-api-delivery-experience-dev.secret"))
    CONTENTFUL_PREVIEW_API_ACCESS_TOKEN  = trimspace(file("${path.module}/../common-secrets/secrets/contentful/contentful-api-preview-experience-dev.secret"))
    VENUE_DISRUPTION_AUTH_KEY            = local.venue_disruption_api_key
  }
}

module "secrets_app_food_ordering" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "app-food-ordering"
  kubernetes_namespace = "food-and-beverage"

  secrets = {
    AMPLITUDE_DEPLOYMENT_API_KEY = trimspace(file("${path.module}/secrets-dev/secrets/amplitude-deployment-api-key.secret"))
    IORDER_BRAND_TOKEN           = trimspace(file("${path.module}/secrets-dev/secrets/iorder-brand-token.secret"))
    IORDER_PREVIEW_BRAND_TOKEN   = trimspace(file("${path.module}/secrets-dev/secrets/iorder-preview-brand-token.secret"))
  }
}
