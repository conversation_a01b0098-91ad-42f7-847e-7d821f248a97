data "aws_security_group" "dms_sg" {
  vpc_id = data.aws_vpc.main.id

  filter {
    name   = "group-name"
    values = ["dev-experience-shared-dms"]
  }
}

module "postgres_service_haven_comms" {
  source                        = "**************:HavenEngineering/tf-rds-instance?ref=v4.2.1"
  resource_name                 = "dev-experience-comms"
  aws_db_parameter_group_suffix = "-v15"

  instance_type      = "db.t4g.small"
  engine_version     = "15.12"
  parameters_family  = "postgres15"
  security_group_ids = [data.aws_security_group.dms_replication_instances.id]
  source_cidr_blocks = local.dev_db_default_allowed_cidrs
  iam_authentication = true
  multi_az           = false

  parameters = [
    {
      name  = "rds.logical_replication",
      value = "1",
    },
    {
      name  = "rds.force_ssl",
      value = "0",
    }
  ]

  additional_instance_tags = {
    "StartInstance" = base64encode("cron(0 2 ? * WED *)|cron(50 5 ? * MON-FRI *)")
    "StopInstance"  = base64encode("cron(0 3 ? * WED *)|cron(05 20 ? * MON-FRI *)")
    "StopOverride"  = "dd.mm.yyyy"
  }

  max_allocated_storage = 1000

  cloudwatch_actions = {
    warning  = data.aws_sns_topic.rds_alerts.arn
    critical = data.aws_sns_topic.rds_alerts.arn
  }

  tags = {
    "aws-migration-project-id" = "MPE16054",
    "map-migrated"             = "d-server-010ojl70n7hps2"
  }
}
