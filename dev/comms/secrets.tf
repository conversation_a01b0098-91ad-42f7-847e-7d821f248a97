resource "random_password" "service_haven_comms_api_key" {
  length  = 16
  special = false
}

module "secrets_service_haven_comms" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "service-haven-comms"
  kubernetes_namespace = "comms"

  secrets = {
    AUTH_KEY                = random_password.service_haven_comms_api_key.result
    DB_USER_NAME            = module.postgres_service_haven_comms.db_username
    DB_USER_PASSWORD        = module.postgres_service_haven_comms.db_password
    DB_HOST                 = module.postgres_service_haven_comms.db_address
    DB_NAME                 = module.postgres_service_haven_comms.db_name
    BLOOMREACH_API_USERNAME = trimspace(file("${path.module}/secrets/bloomreach_api_username.secret"))
    BLOOMREACH_API_PASSWORD = trimspace(file("${path.module}/secrets/bloomreach_api_password.secret"))
  }
}

output "service_haven_comms_api_key" {
  description = "API key for the Comms service"
  value       = random_password.service_haven_comms_api_key.result
  sensitive   = true
}
