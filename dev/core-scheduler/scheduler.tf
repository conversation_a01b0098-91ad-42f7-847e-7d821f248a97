data "aws_iam_policy_document" "scheduler_assume_role" {
  statement {
    actions = ["sts:AssumeRole"]
    effect  = "Allow"
    principals {
      type        = "Service"
      identifiers = ["scheduler.amazonaws.com"]
    }
  }
}

data "aws_iam_policy_document" "scheduler" {
  statement {
    actions = [
      "ec2:StartInstances",
      "lambda:InvokeFunction",
      "rds:StartDBCluster",
      "rds:StartDBInstance",
    ]
    effect = "Allow"
    resources = [
      "arn:aws:ec2:${var.aws_region}:${data.aws_caller_identity.current.account_id}:instance/*",
      "arn:aws:rds:${var.aws_region}:${data.aws_caller_identity.current.account_id}:db:*",
      "arn:aws:rds:${var.aws_region}:${data.aws_caller_identity.current.account_id}:cluster:*",
      module.lambda_scheduler_instance_stop.lambda_function_arn
    ]
  }
}

resource "aws_iam_policy" "scheduler" {
  name        = "haven-${var.module_name}"
  description = "Allow EC2 and RDS Instance Scheduling"
  policy      = data.aws_iam_policy_document.scheduler.json
}

resource "aws_iam_role" "scheduler" {
  name        = "haven-${var.module_name}"
  description = "EC2 and RDS Instance Scheduler Role"
  managed_policy_arns = [
    aws_iam_policy.scheduler.arn
  ]
  assume_role_policy = data.aws_iam_policy_document.scheduler_assume_role.json

}
