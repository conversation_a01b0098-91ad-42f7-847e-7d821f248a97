#
# eventbridge callback
#
module "lambda_scheduler_eventbridge_callback" {
  source  = "terraform-aws-modules/lambda/aws"
  version = "7.20.1"

  function_name = "haven-${var.module_name}-eventbridge-callback"
  handler       = "main.lambda_handler"
  runtime       = "python3.12"
  architectures = ["arm64"]
  timeout       = 30
  publish       = true

  cloudwatch_logs_retention_in_days = 90

  environment_variables = {
    LOG_LEVEL = "INFO"
    TIMEZONE  = "Europe/London"
  }

  allowed_triggers = {
    events = {
      principal  = "events.amazonaws.com"
      source_arn = module.eventbridge.eventbridge_rule_arns.haven-core-scheduler-instance-tag-updates
    }
  }

  attach_policy_statements = true
  policy_statements = {
    scheduler = {
      effect = "Allow"
      actions = [
        "iam:PassRole",
        "scheduler:CreateScheduleGroup",
        "scheduler:DeleteScheduleGroup",
        "scheduler:GetScheduleGroup",
        "scheduler:CreateSchedule",
        "scheduler:DeleteSchedule",
        "scheduler:ListSchedules"
      ]
      resources = ["*"]
    }
    trigger = {
      effect = "Allow"
      actions = [
        "ec2:StartInstances",
        "lambda:InvokeFunction",
        "rds:StartDBCluster",
        "rds:StartDBInstance"
      ]
      resources = [
        "arn:aws:ec2:${var.aws_region}:${data.aws_caller_identity.current.account_id}:instance/*",
        "arn:aws:rds:${var.aws_region}:${data.aws_caller_identity.current.account_id}:db:*",
        "arn:aws:rds:${var.aws_region}:${data.aws_caller_identity.current.account_id}:cluster:*",
        module.lambda_scheduler_instance_stop.lambda_function_arn
      ]
    }
  }

  source_path = [
    {
      path             = "../../shared/${var.module_name}/scheduler-eventbridge-callback"
      pip_requirements = true
    }
  ]
  recreate_missing_package = false

}


#
# eventbridge callback
#
module "lambda_scheduler_instance_stop" {
  source  = "terraform-aws-modules/lambda/aws"
  version = "7.20.1"

  function_name = "haven-${var.module_name}-instance-stop"
  handler       = "main.lambda_handler"
  runtime       = "python3.12"
  architectures = ["arm64"]
  timeout       = 30
  publish       = true

  cloudwatch_logs_retention_in_days = 90

  environment_variables = {
    LOG_LEVEL = "INFO"
  }

  allowed_triggers = {
    scheduler = {
      principal  = "scheduler.amazonaws.com"
      source_arn = "arn:aws:scheduler:${var.aws_region}:${data.aws_caller_identity.current.account_id}:schedule/*"
    }
  }

  attach_policy_statements = true
  policy_statements = {
    ec2-stop-and-read-tags = {
      effect    = "Allow"
      actions   = ["ec2:DescribeTags", "ec2:StopInstances"]
      resources = ["*"]
    }
    ec2-modify-tags = {
      effect    = "Allow"
      actions   = ["ec2:CreateTags", "ec2:DeleteTags"]
      resources = ["arn:aws:ec2:*:*:instance/*"]
      condition = {
        one = {
          test     = "ForAllValues:StringEquals"
          variable = "aws:TagKeys"
          values   = ["StopOverride"]
        }
      }
    }
    rds-stop = {
      effect  = "Allow"
      actions = ["rds:StopDBCluster", "rds:StopDBInstance"]
      resources = [
        "arn:aws:rds:*:*:db:*",
        "arn:aws:rds:*:*:cluster:*"
      ]
    }
    rds-tagging = {
      effect  = "Allow"
      actions = ["rds:AddTagsToResource", "rds:RemoveTagsFromResource", "rds:ListTagsForResource"]
      resources = [
        "arn:aws:rds:*:*:db:*",
        "arn:aws:rds:*:*:cluster:*"
      ]
      condition = {
        one = {
          test     = "ForAllValues:StringEquals"
          variable = "aws:TagKeys"
          values   = ["StopOverride"]
        }
      }
    }
  }

  source_path = [
    {
      path = "../../shared/${var.module_name}/scheduler-instance-stop"
    }
  ]
  recreate_missing_package = false

}


#
# rds instance started
#
module "lambda_scheduler_rds_instance_started" {
  source  = "terraform-aws-modules/lambda/aws"
  version = "7.20.1"

  function_name = "haven-${var.module_name}-rds-instance-started"
  handler       = "main.lambda_handler"
  runtime       = "python3.12"
  architectures = ["arm64"]
  timeout       = 30
  publish       = true

  cloudwatch_logs_retention_in_days = 90

  environment_variables = {
    LOG_LEVEL = "INFO"
    TIMEZONE  = "Europe/London"
  }

  allowed_triggers = {
    events = {
      principal  = "events.amazonaws.com"
      source_arn = module.eventbridge.eventbridge_rule_arns.haven-core-scheduler-rds-instance-started
    }
  }

  attach_policy_statements = true
  policy_statements = {
    dms = {
      effect = "Allow"
      actions = [
        "dms:DescribeEndpoints",
        "dms:DescribeReplicationTasks",
        "dms:StartReplicationTask",
        "rds:DescribeDBInstances"
      ]
      resources = ["*"]
    }
  }

  source_path = [
    {
      path = "../../shared/${var.module_name}/scheduler-rds-instance-started"
    }
  ]
  recreate_missing_package = false

}
