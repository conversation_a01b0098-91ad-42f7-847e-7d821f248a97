#
# scheduler
#
module "eventbridge" {
  source  = "terraform-aws-modules/eventbridge/aws"
  version = "3.9.0"

  create_bus = false

  role_name = "haven-${var.module_name}-eventbridge"

  rules = {

    "haven-${var.module_name}-instance-tag-updates" = {
      description = "Capture EC2 and RDS instance tag updates"
      event_pattern = jsonencode(
        {
          source      = ["aws.tag"]
          detail-type = ["Tag Change on Resource"]
          detail = {
            service          = ["ec2", "rds"]
            changed-tag-keys = ["StartInstance", "StopInstance"]
            resource-type    = ["instance", "db"]
          }
        }
      )
    }

    "haven-${var.module_name}-rds-instance-started" = {
      description = "Capture RDS instance started events"
      event_pattern = jsonencode(
        {
          source      = ["aws.rds"]
          detail-type = ["RDS DB Cluster Event", "RDS DB Instance Event"]
          detail = {
            SourceType = ["DB_CLUSTER", "DB_INSTANCE"]
            Message    = ["Performance Insights has been enabled"]
          }
        }
      )
    }
  }

  targets = {

    "haven-${var.module_name}-instance-tag-updates" = [
      {
        name = "haven-${var.module_name}-instance-tag-updates"
        arn  = module.lambda_scheduler_eventbridge_callback.lambda_function_arn
      }
    ]

    "haven-${var.module_name}-rds-instance-started" = [
      {
        name = "haven-${var.module_name}-rds-instance-started"
        arn  = module.lambda_scheduler_rds_instance_started.lambda_function_arn
      }
    ]

  }
}
