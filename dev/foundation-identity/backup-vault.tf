# Provider for tooling account (destination account where backups are stored)
provider "aws" {
  alias   = "tooling"
  profile = "bourne-tooling"
  region  = "eu-west-1"
}

# KMS key for backup vault encryption
resource "aws_kms_key" "backup_vault_key" {
  description             = "KMS key for cross-account RDS backup vault encryption"
  deletion_window_in_days = 30
  enable_key_rotation     = true

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "Enable IAM User Permissions"
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
        }
        Action   = "kms:*"
        Resource = "*"
      },
      {
        Sid    = "Allow use of the key for AWS Backup"
        Effect = "Allow"
        Principal = {
          Service = "backup.amazonaws.com"
        }
        Action = [
          "kms:Decrypt",
          "kms:GenerateDataKey"
        ]
        Resource = "*"
      },
      {
        Sid    = "Allow attachment of persistent resources"
        Effect = "Allow"
        Principal = {
          Service = "backup.amazonaws.com"
        }
        Action = [
          "kms:CreateGrant",
          "kms:ListGrants",
          "kms:RevokeGrant"
        ]
        Resource = "*"
        Condition = {
          Bool = {
            "kms:GrantIsForAWSResource" = "true"
          }
        }
      },
      {
        Sid    = "Allow dev account to use the key"
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::************:root"
        }
        Action = [
          "kms:Decrypt",
          "kms:GenerateDataKey",
          "kms:CreateGrant",
          "kms:DescribeKey"
        ]
        Resource = "*"
        Condition = {
          StringEquals = {
            "kms:ViaService" = [
              "backup.${data.aws_region.current.name}.amazonaws.com"
            ]
          }
        }
      },
      {
        Sid    = "Allow tooling account to use the key"
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::************:root"
        }
        Action = [
          "kms:Decrypt",
          "kms:GenerateDataKey",
          "kms:CreateGrant",
          "kms:DescribeKey"
        ]
        Resource = "*"
        Condition = {
          StringEquals = {
            "kms:ViaService" = [
              "backup.${data.aws_region.current.name}.amazonaws.com"
            ]
          }
        }
      }
    ]
  })

  tags = {
    Name        = "cross-account-rds-backup-key"
    Environment = var.environment
    Purpose     = "Cross-Account-RDS-Backup-Encryption"
  }
}

resource "aws_kms_alias" "backup_vault_key_alias" {
  name          = "alias/cross-account-rds-backup"
  target_key_id = aws_kms_key.backup_vault_key.key_id
}

# Destination account backup vault (where backups are stored - tooling account)
module "backup_destination" {
  source = "**************:HavenEngineering/tf-backup-vault?ref=v0.0.5"

  # Basic configuration
  vault_name  = "${var.environment}-rds-backup-vault"
  kms_key_arn = aws_kms_key.backup_vault_key.arn

  # Cross-account configuration - this is the destination
  enable_cross_account_backup = true
  cross_account_mode          = "destination"
  source_account_id           = "************" # dev account (source)
  cross_account_role_name     = "CrossAccountBackupRole"

  # SNS notifications disabled for cross-account destination vault
  enable_notifications = false
  sns_topic_arn        = null

  providers = {
    aws        = aws.tooling # Deploy to tooling account
    aws.source = aws.tooling # Required for module
  }

  tags = {
    Purpose       = "RDS-Cross-Account-Backup-Destination"
    SourceAccount = "************" # dev (source account)
    TargetAccount = "************" # tooling (destination account)
    BackupType    = "RDS"
  }
}


# Source account backup configuration (dev account - where RDS resources are located)
resource "aws_iam_role" "cross_account_backup_dev" {
  name = "CrossAccountBackupRole"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Service = "backup.amazonaws.com"
        }
        Action = "sts:AssumeRole"
      }
    ]
  })

  tags = {
    Name        = "CrossAccountBackupRole"
    Environment = var.environment
    Purpose     = "Cross-Account-Backup-Source"
    Account     = "dev"
  }
}

resource "aws_iam_role_policy" "cross_account_backup_dev" {
  name = "CrossAccountBackupPolicy"
  role = aws_iam_role.cross_account_backup_dev.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "backup:StartCopyJob",
          "backup:DescribeBackupVault",
          "backup:ListRecoveryPointsByBackupVault",
          "backup:GetRecoveryPointRestoreMetadata"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "tag:GetResources",
          "tag:DescribeReportCreation",
          "tag:GetComplianceSummary",
          "tag:GetTagKeys",
          "tag:GetTagValues"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "rds:DescribeDBInstances",
          "rds:DescribeDBClusters",
          "rds:ListTagsForResource"
        ]
        Resource = "*"
      },
      {
        Effect   = "Allow"
        Action   = "sts:AssumeRole"
        Resource = module.backup_destination.destination_vault_access_role_arn
      },
      {
        Effect = "Allow"
        Action = [
          "kms:Decrypt",
          "kms:DescribeKey",
          "kms:GenerateDataKey"
        ]
        Resource = "*"
      }
    ]
  })
}

# Local backup vault in dev account (source)
resource "aws_backup_vault" "dev_local" {
  name        = "${var.environment}-dev-local-vault"
  kms_key_arn = aws_kms_key.backup_vault_key.arn

  tags = {
    Name        = "${var.environment}-dev-local-vault"
    Environment = var.environment
    Purpose     = "Local-Backups-Before-Cross-Account-Copy"
    Account     = "dev"
  }
}

# Backup plan in dev account (source)
resource "aws_backup_plan" "cross_account_rds" {
  name = "${var.environment}-cross-account-rds-backup"

  depends_on = [
    aws_backup_vault.dev_local,
    module.backup_destination
  ]

  rule {
    rule_name                = "daily_backup_with_cross_account_copy"
    target_vault_name        = aws_backup_vault.dev_local.name
    schedule                 = "cron(0 2 * * ? *)" # Daily at 2 AM UTC
    start_window             = 60                  # 1 hour
    completion_window        = 120                 # 2 hours
    enable_continuous_backup = false

    lifecycle {
      delete_after = 7 # Keep local backups for 7 days
    }

    copy_action {
      destination_vault_arn = module.backup_destination.backup_vault_arn

      lifecycle {
        cold_storage_after = 30
        delete_after       = 120
      }
    }

    recovery_point_tags = {
      BackupType    = "RDS"
      Environment   = var.environment
      CrossAccount  = "true"
      SourceAccount = "dev"
      TargetAccount = "tooling"
      CreatedBy     = "terraform"
    }
  }

  tags = {
    Name        = "${var.environment}-cross-account-rds-backup"
    Environment = var.environment
    Purpose     = "RDS-Cross-Account-Backup"
    Account     = "dev"
  }
}

resource "aws_backup_selection" "dev_rds" {
  iam_role_arn = aws_iam_role.cross_account_backup_dev.arn
  name         = "${var.environment}-dev-rds-selection"
  plan_id      = aws_backup_plan.cross_account_rds.id

  depends_on = [
    aws_backup_plan.cross_account_rds,
    aws_iam_role_policy.cross_account_backup_dev
  ]

  selection_tag {
    type  = "STRINGEQUALS"
    key   = "Environment"
    value = var.environment
  }

  selection_tag {
    type  = "STRINGEQUALS"
    key   = "BackupRequired"
    value = "true"
  }
}


