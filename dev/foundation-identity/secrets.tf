data "terraform_remote_state" "experience_activities_dev" {
  backend = "s3"

  config = {
    bucket  = var.terraform_state_bucket
    key     = "dev/experience-activities"
    profile = var.terraform_state_profile
    region  = var.terraform_state_region
  }
}

module "secrets_service_haven_identity_migration" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "service-haven-identity-migration"
  kubernetes_namespace = "dev-identity"

  secrets = {
    DATABASE_URL          = "postgresql://${module.postgres_service_haven_identity.db_username}:${module.postgres_service_haven_identity.db_password}@${module.postgres_service_haven_identity.db_endpoint}/${module.postgres_service_haven_identity.db_name}"
    SEAWARE_MAPPING       = trimspace(file("${path.module}/secrets/seaware-mapping.secret"))
    SNOWFLAKE_CREDENTIALS = trimspace(file("${path.module}/secrets/snowflake-credentials.secret"))
    SNOWFLAKE_KEY         = trimspace(file("${path.module}/secrets/snowflake_management_key.p8.secret"))
  }
}

module "secrets_service_haven_identity_hid" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "service-haven-identity-hid"
  kubernetes_namespace = "dev-identity"

  secrets = {
    DATABASE_URL            = "postgresql://${module.postgres_service_haven_identity.db_username}:${module.postgres_service_haven_identity.db_password}@${module.postgres_service_haven_identity.db_endpoint}/${module.postgres_service_haven_identity.db_name}"
    AUTHENTICATION_CONFIG   = trimspace(file("${path.module}/secrets/api-authentication.json.secret"))
    PLOT_API_KEY            = trimspace(file("${path.module}/secrets/plot-api-key.secret"))
    BLAPI_X_API_KEY         = trimspace(file("${path.module}/secrets/blapi-x-api-key.secret"))
    BLAPI_X_IDENTITY_KEY    = trimspace(file("${path.module}/secrets/blapi-x-identity-key.secret"))
    BLAPI_ACCOUNT_X_API_KEY = trimspace(file("${path.module}/secrets/blapi-account-x-api-key.secret"))
    JWKS                    = trimspace(file("${path.module}/secrets/identity-jwks.secret"))
    IDENTITY_SSO_API_KEY    = trimspace(file("${path.module}/secrets/identity-sso-api-key.secret"))
    UNLEASH_API_KEY         = trimspace(file("${path.module}/secrets/unleash-api-key.secret"))
  }
}

module "secrets_service_haven_identity" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "service-haven-identity"
  kubernetes_namespace = "dev-identity"

  secrets = {
    DATABASE_URL                   = "postgresql://${module.postgres_service_haven_identity.db_username}:${module.postgres_service_haven_identity.db_password}@${module.postgres_service_haven_identity.db_endpoint}/${module.postgres_service_haven_identity.db_name}"
    OIDC_DATABASE_URL              = "postgresql://${module.postgres_service_haven_identity.db_username}:${module.postgres_service_haven_identity.db_password}@${module.postgres_service_haven_identity.db_endpoint}/${module.postgres_service_haven_identity.db_name}?schema=oidc"
    PLOT_API_KEY                   = trimspace(file("${path.module}/secrets/plot-api-key.secret"))
    BLAPI_X_API_KEY                = trimspace(file("${path.module}/secrets/blapi-x-api-key.secret"))
    BLAPI_X_IDENTITY_KEY           = trimspace(file("${path.module}/secrets/blapi-x-identity-key.secret"))
    BLAPI_ACCOUNT_X_API_KEY        = trimspace(file("${path.module}/secrets/blapi-account-x-api-key.secret"))
    COOKIE_ENCRYPTION_KEYS         = trimspace(file("${path.module}/secrets/identity-cookie-keys.secret"))
    JWKS                           = trimspace(file("${path.module}/secrets/identity-jwks.secret"))
    HAVEN_TEST_CLIENT_SECRET       = trimspace(file("${path.module}/secrets/identity-client-haven-test.secret"))
    HAVEN_SPA_CLIENT_SECRET        = trimspace(file("${path.module}/secrets/identity-client-haven-spa.secret"))
    HAVEN_MYACCOUNT_CLIENT_SECRET  = trimspace(file("${path.module}/secrets/identity-client-myaccount.secret"))
    HAVEN_OWNERS_CLIENT_SECRET     = trimspace(file("${path.module}/secrets/identity-client-owners.secret"))
    HAVEN_EXPERIENCE_CLIENT_SECRET = trimspace(file("${path.module}/secrets/identity-client-experience.secret"))
    MICROSOFT_CLIENT_SECRET        = trimspace(file("${path.module}/secrets/microsoft-social-login.secret"))
    GOOGLE_CLIENT_SECRET           = trimspace(file("${path.module}/secrets/google-social-login.secret"))
    APPLE_CLIENT_SECRET            = trimspace(file("${path.module}/secrets/appleid-social-login.secret"))
    UNLEASH_API_KEY                = trimspace(file("${path.module}/secrets/unleash-api-key.secret"))
    RECAPTCHA_API_KEY              = trimspace(file("${path.module}/secrets/recaptcha-api-key.secret"))
    IDENTITY_SSO_API_KEY           = trimspace(file("${path.module}/secrets/identity-sso-api-key.secret"))
  }
}

module "secrets_app_haven_identity_admin" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "app-haven-identity-admin"
  kubernetes_namespace = "dev-identity"

  secrets = {
    DATABASE_URL                         = "postgresql://${module.postgres_service_haven_identity.db_username}:${module.postgres_service_haven_identity.db_password}@${module.postgres_service_haven_identity.db_endpoint}/${module.postgres_service_haven_identity.db_name}"
    PLOT_API_KEY                         = trimspace(file("${path.module}/secrets/plot-api-key.secret"))
    BLAPI_X_API_KEY                      = trimspace(file("${path.module}/secrets/blapi-x-api-key.secret"))
    BLAPI_X_IDENTITY_KEY                 = trimspace(file("${path.module}/secrets/blapi-x-identity-key.secret"))
    JWKS                                 = trimspace(file("${path.module}/secrets/identity-jwks.secret"))
    HID_API_KEY                          = trimspace(file("${path.module}/secrets/hid-api-admin-key.secret"))
    IDENTITY_SSO_API_KEY                 = trimspace(file("${path.module}/secrets/identity-sso-api-key.secret"))
    CONTENTFUL_DELIVERY_API_ACCESS_TOKEN = trimspace(file("${path.module}/../common-secrets/secrets/contentful/contentful-api-delivery-experience-dev.secret"))
    ACTIVITIES_API_KEY                   = data.terraform_remote_state.experience_activities_dev.outputs.team_gateway_api_key
  }
}

module "secrets_app_haven_identity" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "app-haven-identity"
  kubernetes_namespace = "dev-identity"

  secrets = {
    UNLEASH_FRONTEND_API_KEY = trimspace(file("${path.module}/secrets/unleash-frontend-api-key.secret"))
    IDENTITY_SSO_API_KEY     = trimspace(file("${path.module}/secrets/identity-sso-api-key.secret"))
  }
}
