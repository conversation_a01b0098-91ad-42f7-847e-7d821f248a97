# Outputs
output "backup_vault_arn" {
  description = "ARN of the cross-account backup vault"
  value       = module.backup_destination.backup_vault_arn
}

output "backup_vault_id" {
  description = "ID of the cross-account backup vault"
  value       = module.backup_destination.backup_vault_id
}

output "backup_plan_id" {
  description = "ID of the backup plan in source account"
  value       = aws_backup_plan.cross_account_rds.id
}

output "backup_selection_id" {
  description = "ID of the backup selection in source account"
  value       = aws_backup_selection.dev_rds.id
}

output "source_cross_account_role_arn" {
  description = "ARN of the cross-account backup role in source account"
  value       = aws_iam_role.cross_account_backup_dev.arn
}

output "destination_vault_access_role_arn" {
  description = "ARN of the vault access role in destination account"
  value       = module.backup_destination.destination_vault_access_role_arn
}

output "kms_key_arn" {
  description = "ARN of the KMS key used for backup encryption"
  value       = aws_kms_key.backup_vault_key.arn
}

output "dev_local_vault_name" {
  description = "Name of the local backup vault in dev account"
  value       = aws_backup_vault.dev_local.name
}
