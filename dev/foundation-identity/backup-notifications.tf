# SNS topic for backup notifications

resource "aws_sns_topic" "backup_notifications" {
  name              = "cross-account-rds-backup-notifications"
  kms_master_key_id = aws_kms_key.backup_vault_key.id

  tags = {
    Name        = "cross-account-rds-backup-notifications"
    Environment = var.environment
    Purpose     = "Backup-Notifications"
  }
}

resource "aws_sns_topic_policy" "backup_notifications" {
  arn = aws_sns_topic.backup_notifications.arn

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "AllowBackupServiceToPublish"
        Effect = "Allow"
        Principal = {
          Service = "backup.amazonaws.com"
        }
        Action = [
          "SNS:Publish"
        ]
        Resource = aws_sns_topic.backup_notifications.arn
      }
    ]
  })
}

resource "aws_sns_topic_subscription" "backup_email_notifications" {
  count     = var.backup_notification_email != "" ? 1 : 0
  topic_arn = aws_sns_topic.backup_notifications.arn
  protocol  = "email"
  endpoint  = var.backup_notification_email
}