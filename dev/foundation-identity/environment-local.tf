variable "environment" {
  description = "Environment name (e.g., dev, staging, prod)"
  type        = string
  default     = "dev"
}

variable "backup_notification_email" {
  description = "Email address for backup notifications (optional)"
  type        = string
  default     = "<EMAIL>"
}

variable "common_tags" {
  description = "Common tags to apply to all resources"
  type        = map(string)
  default = {
    ManagedBy = "Terraform"
    Service   = "Backup"
  }
}
