module "postgres_service_haven_identity" {
  source        = "**************:HavenEngineering/tf-rds-instance?ref=v4.2.0"
  resource_name = "dev-foundation-identity"

  engine_version                = "15.12"
  instance_type                 = "db.t4g.small"
  parameters_family             = "postgres15"
  source_cidr_blocks            = local.dev_db_default_allowed_cidrs
  multi_az                      = false
  disable_cloudwatch_alarms     = true
  aws_db_parameter_group_suffix = "-v15" # not really convinced this is needed
  parameters = [
    {
      name  = "rds.force_ssl",
      value = "0",
  }]
  cloudwatch_actions = {
    warning  = ""
    critical = ""
  }
  additional_instance_tags = {
    "StartInstance" : base64encode("cron(0 2 ? * WED *)|cron(50 5 ? * MON-FRI *)")
    "StopInstance" : base64encode("cron(0 3 ? * WED *)|cron(05 20 ? * MON-FRI *)")
    "StopOverride" : "dd.mm.yyyy"
    "BackupRequired" : "true"
    "Environment" : var.environment
  }
}
