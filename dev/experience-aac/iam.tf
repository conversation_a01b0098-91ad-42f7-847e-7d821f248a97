# TODO: Refactor name of this iam policy document, since it's going to be used only by service-haven-arrivals-sync
# TODO: Support both for now
data "aws_iam_policy_document" "service_haven_holiday_bookings" {
  statement {
    effect    = "Allow"
    actions   = ["s3:ListBucket"]
    resources = ["arn:aws:s3:::aws-haven-dataupload-dev-s3"]
  }

  statement {
    effect    = "Allow"
    actions   = ["s3:GetObject"]
    resources = ["arn:aws:s3:::aws-haven-dataupload-dev-s3/*"]
  }

  statement {
    effect    = "Allow"
    actions   = ["s3:ListBucket"]
    resources = ["${module.s3_bucket_arrivals_sync.bucket_arn}"]
  }

  statement {
    effect    = "Allow"
    actions   = ["s3:GetObject"]
    resources = ["${module.s3_bucket_arrivals_sync.bucket_arn}/*"]
  }
}

data "aws_iam_policy_document" "service_haven_my_haven" {
  statement {
    effect    = "Allow"
    actions   = ["s3:GetObject"]
    resources = ["${module.s3_bucket_remote_config.bucket_arn}/*"]
  }
}

data "aws_iam_policy_document" "service_haven_teams_gateway" {
  statement {
    effect    = "Allow"
    actions   = ["s3:GetObject"]
    resources = ["${module.s3_bucket_remote_config.bucket_arn}/*"]
  }
}

data "aws_iam_policy_document" "service_haven_arrivals_analytics" {
  statement {
    sid     = "AssumeOutputBucket"
    effect  = "Allow"
    actions = ["sts:AssumeRole"]
    // This role allows the service to write to the output bucket which
    // Snowflake is configured to read from.
    resources = ["arn:aws:iam::************:role/digital-arrivals-snowflake-rol"]
  }

  statement {
    sid     = "ConnectToDBWithIAM"
    effect  = "Allow"
    actions = ["rds-db:connect"]
    resources = [
      "arn:aws:rds-db:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:dbuser:${module.postgres_service_haven_arrivals.db_resource_id}/${local.arrivals_analytics_username}",
    ]
  }
}

# The IAM role trust relationship policy details (principal and externalId) are
# provided by Snowflake and are related to the export process setup there.
data "aws_iam_policy_document" "arrivals_sync_snowflake_assume" {
  statement {
    effect = "Allow"
    actions = [
      "sts:AssumeRole",
    ]

    condition {
      test     = "StringEquals"
      variable = "sts:ExternalId"
      values   = ["BD78472_SFCRole=921_7jDA0eNzlU6KEpcIddi0zfSORig="]
    }

    principals {
      identifiers = ["arn:aws:iam::************:user/m5a8-s-iest2078"]
      type        = "AWS"
    }
  }
}

# This access policy is based on the recommended policy from Snowflake but
# without any prefix conditions. This role can be re-used for multiple
# integrations within Seaware, each using a different prefix. Since the bucket
# is only used for this purpose, it is acceptable to open up permissions.
data "aws_iam_policy_document" "arrivals_sync_snowflake" {
  statement {
    effect = "Allow"

    actions = [
      "s3:PutObject",
      "s3:GetObject",
      "s3:GetObjectVersion",
      "s3:DeleteObject",
      "s3:DeleteObjectVersion",
    ]

    resources = ["${module.s3_bucket_arrivals_sync.bucket_arn}/*"]
  }

  statement {
    effect    = "Allow"
    actions   = ["s3:ListBucket"]
    resources = ["${module.s3_bucket_arrivals_sync.bucket_arn}"]
  }
}


# This role is only used by the Snowflake integration.
resource "aws_iam_role" "arrivals_sync_snowflake" {
  name               = "dev-experience-arrivals-sync-snowflake"
  assume_role_policy = data.aws_iam_policy_document.arrivals_sync_snowflake_assume.json

  inline_policy {
    name   = "s3-bucket-access"
    policy = data.aws_iam_policy_document.arrivals_sync_snowflake.json
  }
}

