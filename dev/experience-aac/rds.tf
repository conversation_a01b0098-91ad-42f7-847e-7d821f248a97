data "aws_security_group" "dms_sg" {
  vpc_id = data.aws_vpc.main.id

  filter {
    name   = "group-name"
    values = ["dev-experience-shared-dms"]
  }
}

module "postgres_service_haven_arrivals" {
  source             = "**************:HavenEngineering/tf-rds-instance?ref=v4.2.0"
  resource_name      = "dev-experience-arrivals"
  instance_type      = "db.t4g.small"
  source_cidr_blocks = local.dev_db_default_allowed_cidrs
  security_group_ids = [data.aws_security_group.dms_replication_instances.id]
  iam_authentication = true
  multi_az           = false
  storage_size       = 100

  parameters_family             = "postgres15"
  aws_db_parameter_group_suffix = "-v15"
  engine_version                = "15.12"
  parameters = [
    {
      name  = "rds.logical_replication",
      value = "1",
    },
    {
      name  = "rds.force_ssl",
      value = "0",
    }
  ]

  additional_instance_tags = {
    "blg:dms" : "experience"
    "StartInstance" : base64encode("cron(0 2 ? * WED *)|cron(50 5 ? * MON-FRI *)")
    "StopInstance" : base64encode("cron(0 3 ? * WED *)|cron(05 20 ? * MON-FRI *)")
    "StopOverride" : "dd.mm.yyyy"
  }

  cloudwatch_actions = {
    warning  = data.aws_sns_topic.rds_alerts.arn
    critical = data.aws_sns_topic.rds_alerts.arn
  }
}

module "postgres_service_haven_park_areas" {
  source        = "**************:HavenEngineering/tf-rds-instance?ref=v4.2.0"
  resource_name = "dev-experience-park-areas"

  instance_type      = "db.t4g.small"
  security_group_ids = [data.aws_security_group.dms_replication_instances.id]
  source_cidr_blocks = local.dev_db_default_allowed_cidrs
  iam_authentication = true
  multi_az           = false

  parameters_family             = "postgres15"
  aws_db_parameter_group_suffix = "-v15"
  engine_version                = "15.12"
  parameters = [
    {
      name  = "rds.logical_replication",
      value = "1",
    },
    {
      name  = "rds.force_ssl",
      value = "0",
    }
  ]

  additional_instance_tags = {
    "blg:dms" : "experience"
    "StartInstance" : base64encode("cron(0 2 ? * WED *)|cron(50 5 ? * MON-FRI *)")
    "StopInstance" : base64encode("cron(0 3 ? * WED *)|cron(05 20 ? * MON-FRI *)")
    "StopOverride" : "dd.mm.yyyy"
  }

  cloudwatch_actions = {
    warning  = data.aws_sns_topic.rds_alerts.arn
    critical = data.aws_sns_topic.rds_alerts.arn
  }
}

module "postgres_service_haven_holiday_bookings" {
  source        = "**************:HavenEngineering/tf-rds-instance?ref=v4.2.0"
  resource_name = "dev-experience-holiday-bookings"

  instance_type = "db.t4g.small"
  multi_az      = false

  parameters_family             = "postgres15"
  aws_db_parameter_group_suffix = "-v15"
  engine_version                = "15.12"
  parameters = [
    {
      name  = "rds.logical_replication",
      value = "1",
    },
    {
      name  = "rds.force_ssl",
      value = "0",
    }
  ]

  additional_instance_tags = {
    "blg:dms" : "experience"
    "StartInstance" : base64encode("cron(0 2 ? * WED *)|cron(50 5 ? * MON-FRI *)")
    "StopInstance" : base64encode("cron(0 3 ? * WED *)|cron(05 20 ? * MON-FRI *)")
    "StopOverride" : "dd.mm.yyyy"
  }

  source_cidr_blocks = local.dev_db_default_allowed_cidrs
  security_group_ids = [data.aws_security_group.dms_replication_instances.id]

  cloudwatch_actions = {
    warning  = data.aws_sns_topic.rds_alerts.arn
    critical = data.aws_sns_topic.rds_alerts.arn
  }
}

module "postgres_service_haven_my_haven" {
  source        = "**************:HavenEngineering/tf-rds-instance?ref=v4.2.0"
  resource_name = "dev-experience-my-haven"

  instance_type      = "db.t4g.small"
  source_cidr_blocks = local.dev_db_default_allowed_cidrs
  iam_authentication = true
  multi_az           = false

  parameters_family             = "postgres15"
  aws_db_parameter_group_suffix = "-v15"
  engine_version                = "15.12"
  parameters = [
    {
      name  = "rds.force_ssl",
      value = "0",
    }
  ]

  cloudwatch_actions = {
    warning  = data.aws_sns_topic.rds_alerts.arn
    critical = data.aws_sns_topic.rds_alerts.arn
  }

  additional_instance_tags = {
    "StartInstance" : base64encode("cron(0 2 ? * WED *)|cron(50 5 ? * MON-FRI *)")
    "StopInstance" : base64encode("cron(0 3 ? * WED *)|cron(05 20 ? * MON-FRI *)")
    "StopOverride" : "dd.mm.yyyy"
  }
}

module "postgres_service_haven_venue_disruption" {
  source        = "**************:HavenEngineering/tf-rds-instance?ref=v4.2.1"
  resource_name = "dev-experience-venue-disruption"

  aws_db_parameter_group_suffix = "-v15"
  instance_type                 = "db.t4g.small"
  engine_version                = "15.12"
  parameters_family             = "postgres15"
  security_group_ids            = [data.aws_security_group.dms_replication_instances.id]
  source_cidr_blocks            = local.dev_db_default_allowed_cidrs
  iam_authentication            = true
  multi_az                      = false

  parameters = [
    {
      name  = "rds.logical_replication",
      value = "1",
    },
    {
      name  = "rds.force_ssl",
      value = "0",
    }
  ]

  additional_instance_tags = {
    "blg:dms" : "team"
    "StartInstance" : base64encode("cron(0 2 ? * WED *)|cron(50 5 ? * MON-FRI *)")
    "StopInstance" : base64encode("cron(0 3 ? * WED *)|cron(05 20 ? * MON-FRI *)")
    "StopOverride" : "dd.mm.yyyy"
  }

  max_allocated_storage = 1000

  cloudwatch_actions = {
    warning  = data.aws_sns_topic.rds_alerts.arn
    critical = data.aws_sns_topic.rds_alerts.arn
  }
}

module "postgres_service_haven_onpark_wifi" {
  source        = "**************:HavenEngineering/tf-rds-instance?ref=v4.2.0"
  resource_name = "dev-experience-onpark-wifi"

  aws_db_parameter_group_suffix = "-v15"

  instance_type         = "db.t4g.small"
  engine_version        = "15.12"
  parameters_family     = "postgres15"
  source_cidr_blocks    = local.dev_db_default_allowed_cidrs
  security_group_ids    = [data.aws_security_group.dms_replication_instances.id]
  iam_authentication    = true
  multi_az              = false
  max_allocated_storage = 1000

  parameters = [
    {
      name  = "rds.force_ssl",
      value = "0",
    },
    {
      name  = "rds.logical_replication",
      value = "1",
    }
  ]

  additional_instance_tags = {
    "blg:dms" : "experience"
    "StartInstance" : base64encode("cron(0 2 ? * WED *)|cron(50 5 ? * MON-FRI *)")
    "StopInstance" : base64encode("cron(0 3 ? * WED *)|cron(05 20 ? * MON-FRI *)")
    "StopOverride" : "dd.mm.yyyy"
  }

  cloudwatch_actions = {
    warning  = data.aws_sns_topic.rds_alerts.arn
    critical = data.aws_sns_topic.rds_alerts.arn
  }
}

module "postgres_service_haven_team_card" {
  source        = "**************:HavenEngineering/tf-rds-instance?ref=v4.2.1"
  resource_name = "dev-experience-team-card"

  aws_db_parameter_group_suffix = "-v15"

  instance_type      = "db.t4g.small"
  engine_version     = "15.12"
  parameters_family  = "postgres15"
  security_group_ids = [data.aws_security_group.dms_replication_instances.id]
  source_cidr_blocks = local.dev_db_default_allowed_cidrs
  iam_authentication = true
  multi_az           = false

  parameters = [
    {
      name  = "rds.logical_replication",
      value = "1",
    },
    {
      name  = "rds.force_ssl",
      value = "0",
    }
  ]

  additional_instance_tags = {
    "blg:dms"            = "team",
    "blg:provisionedby"  = "terraform",
    "blg:serviceowner"   = "team",
    "blg:risk"           = "medium",
    "blg:classification" = "private",
    "blg:access"         = "corp",
    "blg:environment"    = "dev",
    "blg:brand"          = "haven",
    "StartInstance"      = base64encode("cron(0 2 ? * WED *)|cron(50 5 ? * MON-FRI *)")
    "StopInstance"       = base64encode("cron(0 3 ? * WED *)|cron(05 20 ? * MON-FRI *)")
    "StopOverride"       = "dd.mm.yyyy"
  }

  max_allocated_storage = 1000

  cloudwatch_actions = {
    warning  = data.aws_sns_topic.rds_alerts.arn
    critical = data.aws_sns_topic.rds_alerts.arn
  }
}
