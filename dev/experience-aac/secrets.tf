data "terraform_remote_state" "experience_activities" {
  backend = "s3"

  config = {
    bucket  = var.terraform_state_bucket
    key     = "dev/experience-activities"
    profile = var.terraform_state_profile
    region  = var.terraform_state_region
  }
}

data "terraform_remote_state" "ownership_owners" {
  backend = "s3"

  config = {
    bucket  = var.terraform_state_bucket
    key     = "dev/ownership-owners"
    profile = var.terraform_state_profile
    region  = var.terraform_state_region
  }
}

data "terraform_remote_state" "shared_playpass" {
  backend = "s3"

  config = {
    bucket  = var.terraform_state_bucket
    key     = "dev/shared-playpass"
    profile = var.terraform_state_profile
    region  = var.terraform_state_region
  }
}

locals {
  activities_team_gateway_api_key = data.terraform_remote_state.experience_activities.outputs.team_gateway_api_key
  arrivals_analytics_username     = "analytics_viewer"
  owners_arrivals_api_keys        = data.terraform_remote_state.ownership_owners.outputs.service_haven_owners_arrivals_api_keys
  shared_playpass_api_keys        = data.terraform_remote_state.shared_playpass.outputs.api_keys
}

# resources
resource "random_password" "service_haven_arrivals_api_key" {
  length  = 16
  special = false
}

resource "random_password" "service_haven_park_areas_api_key" {
  length  = 16
  special = false
}

resource "random_password" "service_haven_holiday_bookings_api_key" {
  length  = 16
  special = false
}

resource "random_password" "service_haven_arrivals_iclean_api_key" {
  length  = 16
  special = false
}

resource "random_password" "service_haven_arrivals_qualtrics_api_key" {
  length  = 16
  special = false
}

resource "random_password" "service_haven_experience_auth_jwt_secret" {
  length  = 16
  special = false
}

resource "random_password" "service_haven_experience_owners_api_key" {
  length  = 16
  special = false
}

resource "random_password" "service_haven_arrivals_external_api_key" {
  length  = 16
  special = false
}

resource "random_password" "service_haven_weather_api_key" {
  length  = 16
  special = false
}

resource "random_password" "service_haven_venue_disruption_api_key" {
  length  = 16
  special = false
}

resource "random_password" "service_haven_team_card_api_key" {
  length  = 16
  special = false
}

resource "random_password" "service_haven_onpark_wifi_management_api_key" {
  length  = 16
  special = false
}

output "venue_disruption_api_key" {
  description = "API key for the Venues service"
  value       = random_password.service_haven_venue_disruption_api_key.result
  sensitive   = true
}

output "arrivals_api_key" {
  description = "API key for the Arrivals service"
  value       = random_password.service_haven_arrivals_api_key.result
  sensitive   = true
}

output "team_card_api_key" {
  description = "API key for the team card service"
  value       = random_password.service_haven_team_card_api_key.result
  sensitive   = true
}

output "service_haven_park_areas_api_key" {
  description = "API key for the Park Areas service"
  value       = random_password.service_haven_park_areas_api_key.result
  sensitive   = true
}

# services
# service-haven-arrivals
module "secrets_service_haven_arrivals" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "service-haven-arrivals"
  kubernetes_namespace = "dev-exp"

  secrets = {
    DB_USER_NAME              = module.postgres_service_haven_arrivals.db_username
    DB_USER_PASSWORD          = module.postgres_service_haven_arrivals.db_password
    DB_HOST                   = module.postgres_service_haven_arrivals.db_address
    DB_NAME                   = module.postgres_service_haven_arrivals.db_name
    API_KEY                   = random_password.service_haven_arrivals_api_key.result
    HOLIDAY_BOOKINGS_AUTH_KEY = random_password.service_haven_holiday_bookings_api_key.result
    ACTION_ICLEAN_AUTH_KEY    = random_password.service_haven_arrivals_iclean_api_key.result
    ACTION_QUALTRICS_AUTH_KEY = random_password.service_haven_arrivals_qualtrics_api_key.result
    EXTERNAL_API_KEY          = random_password.service_haven_arrivals_external_api_key.result
    ICLEAN_AUTH_KEY           = trimspace(file("${path.module}/secrets-values/dev/iclean_auth_key.secret"))
    BLAPI_BOOKING_AUTH_KEY    = trimspace(file("${path.module}/secrets-values/dev/blapi_booking_auth_key.secret"))
    HID_AUTH_KEY              = trimspace(file("${path.module}/secrets-values/dev/hid_app_key.secret"))
    BLOOMREACH_API_USERNAME   = trimspace(file("${path.module}/secrets-values/dev/bloomreach_api_username.secret"))
    BLOOMREACH_API_PASSWORD   = trimspace(file("${path.module}/secrets-values/dev/bloomreach_api_password.secret"))
  }
}

# service-haven-holiday-bookings
module "secrets_service_haven_holiday_bookings" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "service-haven-holiday-bookings"
  kubernetes_namespace = "dev-exp"

  secrets = {
    DB_USER_NAME                     = module.postgres_service_haven_holiday_bookings.db_username
    DB_USER_PASSWORD                 = module.postgres_service_haven_holiday_bookings.db_password
    DB_HOST                          = module.postgres_service_haven_holiday_bookings.db_address
    DB_NAME                          = module.postgres_service_haven_holiday_bookings.db_name
    AUTH_KEY                         = random_password.service_haven_holiday_bookings_api_key.result
    BOURNE_OCP_APIM_SUBSCRIPTION_KEY = trimspace(file("${path.module}/secrets-values/dev/ocp_apim_subscription_key.secret"))
  }
}

# service-haven-my-haven
module "secrets_service_haven_my_haven" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "service-haven-my-haven"
  kubernetes_namespace = "dev-exp"

  secrets = {
    ARRIVALS_AUTH_KEY                          = random_password.service_haven_arrivals_api_key.result
    HOLIDAY_BOOKINGS_AUTH_KEY                  = random_password.service_haven_holiday_bookings_api_key.result
    OCP_APIM_SUBSCRIPTION_KEY                  = trimspace(file("${path.module}/secrets-values/dev/ocp_apim_subscription_key.secret"))
    LIVE_RES_HOOK_API_KEY                      = trimspace(file("${path.module}/secrets-values/dev/live_res_hook_api_key.secret"))
    SID_AUTH_KEY                               = trimspace(file("${path.module}/secrets-values/dev/sid_api_key.secret"))
    DB_USER_NAME                               = module.postgres_service_haven_my_haven.db_username
    DB_USER_PASSWORD                           = module.postgres_service_haven_my_haven.db_password
    DB_HOST                                    = module.postgres_service_haven_my_haven.db_address
    DB_NAME                                    = module.postgres_service_haven_my_haven.db_name
    BOURNE_AUTH_KEY                            = trimspace(file("${path.module}/secrets-values/dev/bourne_auth_key.secret"))
    BOURNE_IDENTITY_KEY                        = trimspace(file("${path.module}/secrets-values/dev/bourne_identity_key.secret"))
    CONTENTFUL_ACCESS_TOKEN                    = trimspace(file("${path.module}/secrets-values/dev/contentful_access_token.secret"))
    CONTENTFUL_EXPERIENCE_ACCESS_TOKEN         = trimspace(file("${path.module}/secrets-values/dev/contentful_experience_access_token.secret"))
    CONTENTFUL_EXPERIENCE_SPACE_ID             = trimspace(file("${path.module}/secrets-values/dev/contentful_experience_space_id.secret"))
    CONTENTFUL_EXPERIENCE_PREVIEW_ACCESS_TOKEN = trimspace(file("${path.module}/secrets-values/dev/contentful_experience_preview_access_token.secret"))
    VENUE_DISRUPTION_AUTH_KEY                  = random_password.service_haven_venue_disruption_api_key.result
    OWNERS_ARRIVALS_AUTH_KEY                   = local.owners_arrivals_api_keys["service-haven-my-haven"]
    BLAPI_BOOKING_API_KEY                      = trimspace(file("${path.module}/secrets-values/dev/blapi_booking_auth_key.secret"))
    DVLA_AUTH_KEY                              = trimspace(file("${path.module}/secrets-values/dev/dvla_auth_key.secret"))
  }
}

# service-haven-teams-gateway
module "secrets_service_haven_teams_gateway" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "service-haven-teams-gateway"
  kubernetes_namespace = "dev-exp"

  secrets = {
    ARRIVALS_AUTH_KEY                          = random_password.service_haven_arrivals_api_key.result
    OWNERS_ARRIVALS_AUTH_KEY                   = local.owners_arrivals_api_keys["service-haven-teams-gateway"]
    PLAYPASS_AUTH_KEY                          = local.shared_playpass_api_keys["service-haven-teams-gateway"]
    HOLIDAY_BOOKINGS_AUTH_KEY                  = random_password.service_haven_holiday_bookings_api_key.result
    ACTIVITIES_TEAM_GATEWAY_AUTH_KEY           = local.activities_team_gateway_api_key
    SID_AUTH_KEY                               = trimspace(file("${path.module}/secrets-values/dev/sid_api_key.secret"))
    ICLEAN_AUTH_KEY                            = trimspace(file("${path.module}/secrets-values/dev/iclean_auth_key.secret"))
    BLAPI_AUTH_KEY                             = trimspace(file("${path.module}/secrets-values/dev/bourne_auth_key.secret"))
    CONTENTFUL_EXPERIENCE_ACCESS_TOKEN         = trimspace(file("${path.module}/secrets-values/dev/contentful_experience_access_token.secret"))
    CONTENTFUL_EXPERIENCE_SPACE_ID             = trimspace(file("${path.module}/secrets-values/dev/contentful_experience_space_id.secret"))
    CONTENTFUL_EXPERIENCE_PREVIEW_ACCESS_TOKEN = trimspace(file("${path.module}/secrets-values/dev/contentful_experience_preview_access_token.secret"))
  }
}

# service-haven-arrivals-dashboard-gateway
module "secrets_service_haven_arrivals_dashboard_gateway" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "service-haven-arrivals-dashboard-gateway"
  kubernetes_namespace = "dev-exp"

  secrets = {
    ARRIVALS_AUTH_KEY         = random_password.service_haven_arrivals_api_key.result
    OWNERS_ARRIVALS_AUTH_KEY  = local.owners_arrivals_api_keys["service-haven-arrivals-dashboard-gateway"]
    PLAYPASS_AUTH_KEY         = local.shared_playpass_api_keys["service-haven-arrivals-dashboard-gateway"]
    HOLIDAY_BOOKINGS_AUTH_KEY = random_password.service_haven_holiday_bookings_api_key.result
    PARK_AREAS_AUTH_KEY       = random_password.service_haven_park_areas_api_key.result
    WEATHER_AUTH_KEY          = random_password.service_haven_weather_api_key.result
    BLAPI_BOOKING_AUTH_KEY    = trimspace(file("${path.module}/secrets-values/dev/blapi_booking_auth_key.secret"))
  }
}

# service-haven-arrivals-gateway
module "secrets_service_haven_arrivals_gateway" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "service-haven-arrivals-gateway"
  kubernetes_namespace = "dev-exp"

  secrets = {
    ARRIVALS_AUTH_KEY           = random_password.service_haven_arrivals_api_key.result
    OWNERS_ARRIVALS_AUTH_KEY    = local.owners_arrivals_api_keys["service-haven-arrivals-gateway"]
    HOLIDAY_BOOKINGS_AUTH_KEY   = random_password.service_haven_holiday_bookings_api_key.result
    ARRIVALS_GATEWAY_JWT_SECRET = random_password.service_haven_experience_auth_jwt_secret.result
    PARK_AREAS_AUTH_KEY         = random_password.service_haven_park_areas_api_key.result
  }
}

# service-haven-arrivals-sync
module "secrets_service_haven_arrivals_sync" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "service-haven-arrivals-sync"
  kubernetes_namespace = "dev-exp"

  secrets = {
    ARRIVALS_AUTH_KEY                = random_password.service_haven_arrivals_api_key.result
    HOLIDAY_BOOKINGS_AUTH_KEY        = random_password.service_haven_holiday_bookings_api_key.result
    BOURNE_OCP_APIM_SUBSCRIPTION_KEY = trimspace(file("${path.module}/secrets-values/dev/ocp_apim_subscription_key.secret"))
    BOURNE_AUTH_KEY                  = trimspace(file("${path.module}/secrets-values/dev/bourne_auth_key.secret"))
    BLAPI_BOOKING_AUTH_KEY           = trimspace(file("${path.module}/secrets-values/dev/blapi_booking_auth_key.secret"))
  }
}

# service-haven-dev-booking-broker
module "secrets_service_haven_dev_booking_broker" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "service-haven-dev-booking-broker"
  kubernetes_namespace = "dev-exp"

  secrets = {
    ARRIVALS_AUTH_KEY         = random_password.service_haven_arrivals_api_key.result
    HOLIDAY_BOOKINGS_AUTH_KEY = random_password.service_haven_holiday_bookings_api_key.result
    PARK_AREAS_AUTH_KEY       = random_password.service_haven_park_areas_api_key.result
  }
}

# service-haven-park-areas
module "secrets_service_haven_park_areas" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "service-haven-park-areas"
  kubernetes_namespace = "dev-exp"

  secrets = {
    DB_USER_NAME     = module.postgres_service_haven_park_areas.db_username
    DB_USER_PASSWORD = module.postgres_service_haven_park_areas.db_password
    DB_HOST          = module.postgres_service_haven_park_areas.db_address
    DB_NAME          = module.postgres_service_haven_park_areas.db_name
    AUTH_KEY         = random_password.service_haven_park_areas_api_key.result
  }
}

# service-haven-web-gateway
module "secrets_service_haven_web_gateway" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "service-haven-web-gateway"
  kubernetes_namespace = "dev-exp"

  secrets = {
    ARRIVALS_AUTH_KEY         = random_password.service_haven_arrivals_api_key.result
    HOLIDAY_BOOKINGS_AUTH_KEY = random_password.service_haven_holiday_bookings_api_key.result
    AUTH_SERVICE_JWT_SECRET   = random_password.service_haven_experience_auth_jwt_secret.result
    OWNERS_ARRIVALS_AUTH_KEY  = local.owners_arrivals_api_keys["service-haven-web-gateway"]
    ICLEAN_AUTH_KEY           = trimspace(file("${path.module}/secrets-values/dev/iclean_auth_key.secret"))
    PARK_AREAS_AUTH_KEY       = random_password.service_haven_park_areas_api_key.result
  }
}

module "secrets_service_haven_weather" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "service-haven-weather"
  kubernetes_namespace = "dev-exp"

  secrets = {
    AUTH_KEY              = random_password.service_haven_weather_api_key.result
    OPEN_WEATHER_AUTH_KEY = trimspace(file("${path.module}/secrets-values/dev/open_weather_api_key.secret"))
    PARK_AREAS_AUTH_KEY   = random_password.service_haven_park_areas_api_key.result
  }
}

# service-haven-venue-disruption
module "secrets_service_haven_venue_disruption" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "service-haven-venue-disruption"
  kubernetes_namespace = "dev-exp"

  secrets = {
    AUTH_KEY            = random_password.service_haven_venue_disruption_api_key.result
    DB_USER_NAME        = module.postgres_service_haven_venue_disruption.db_username
    DB_USER_PASSWORD    = module.postgres_service_haven_venue_disruption.db_password
    DB_HOST             = module.postgres_service_haven_venue_disruption.db_address
    DB_NAME             = module.postgres_service_haven_venue_disruption.db_name
    PARK_AREAS_AUTH_KEY = random_password.service_haven_park_areas_api_key.result
    CELLO_API_KEY       = trimspace(file("${path.module}/secrets-values/dev/cello_api_key.secret"))
  }
}

# service-haven-team-card
module "secrets_service_haven_team_card" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "service-haven-team-card"
  kubernetes_namespace = "dev-exp"

  secrets = {
    AUTH_KEY         = random_password.service_haven_team_card_api_key.result
    DB_USER_NAME     = module.postgres_service_haven_team_card.db_username
    DB_USER_PASSWORD = module.postgres_service_haven_team_card.db_password
    DB_HOST          = module.postgres_service_haven_team_card.db_address
    DB_NAME          = module.postgres_service_haven_team_card.db_name
    EES_AUTH_SECRET  = trimspace(file("${path.module}/secrets-values/dev/ees-auth-secret.secret"))
    SFTP_HOST        = trimspace(file("${path.module}/secrets-values/dev/sftp-host.secret"))
    SFTP_USERNAME    = trimspace(file("${path.module}/secrets-values/dev/sftp-username.secret"))
    SFTP_PASSWORD    = trimspace(file("${path.module}/secrets-values/dev/sftp-password.secret"))
  }
}

# service-haven-onpark-wifi
module "secrets_service_haven_onpark_wifi" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "service-haven-onpark-wifi"
  kubernetes_namespace = "dev-exp"

  secrets = {
    DB_USER_NAME           = module.postgres_service_haven_onpark_wifi.db_username
    DB_USER_PASSWORD       = module.postgres_service_haven_onpark_wifi.db_password
    DB_HOST                = module.postgres_service_haven_onpark_wifi.db_address
    DB_NAME                = module.postgres_service_haven_onpark_wifi.db_name
    MANAGEMENT_API_KEY     = random_password.service_haven_onpark_wifi_management_api_key.result
    ACS_USERNAME           = trimspace(file("${path.module}/secrets-values/dev/acs_username.secret"))
    ACS_PASSWORD           = trimspace(file("${path.module}/secrets-values/dev/acs_password.secret"))
    ESIM_GO_API_KEY        = trimspace(file("${path.module}/secrets-values/dev/esim_go_api_key.secret"))
    BLAPI_BOOKING_API_KEY  = trimspace(file("${path.module}/secrets-values/dev/blapi_booking_auth_key.secret"))
    HAVEN_PAYMENTS_API_KEY = trimspace(file("${path.module}/secrets-values/dev/haven_payments_wifi_api_key.secret"))
  }
}

output "wifi_service_management_api_key" {
  description = "Wifi Service Management API key"
  value       = random_password.service_haven_onpark_wifi_management_api_key.result
  sensitive   = true
}

output "holiday_bookings_service_api_key" {
  description = "Holiday bookings service API key"
  value       = random_password.service_haven_holiday_bookings_api_key.result
  sensitive   = true
}

output "arrivals_service_auth_key" {
  description = "Arrivals service auth key"
  value       = random_password.service_haven_arrivals_api_key.result
  sensitive   = true
}
