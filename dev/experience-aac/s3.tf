module "s3_bucket_remote_config" {
  source        = "**************:HavenEngineering/tf-s3-bucket?ref=v3.0.1"
  resource_name = "haven-dev-experience-remote-config"
}

module "s3_bucket_oas_specs" {
  source        = "**************:HavenEngineering/tf-s3-bucket?ref=v3.0.1"
  resource_name = "haven-dev-experience-oas-specs"
}


data "aws_iam_policy_document" "bucket_policy_crossaccount" {
  statement {
    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::************:root"]
    }
    effect    = "Allow"
    actions   = ["s3:*"]
    resources = ["${module.s3_bucket_oas_specs.bucket_arn}/*"]
  }
}

resource "aws_s3_bucket_policy" "crossaccount" {
  bucket = module.s3_bucket_oas_specs.bucket_name
  policy = data.aws_iam_policy_document.bucket_policy_crossaccount.json
}

module "s3_bucket_arrivals_sync" {
  source        = "**************:HavenEngineering/tf-s3-bucket?ref=v3.0.1"
  resource_name = "haven-dev-experience-arrivals-sync"
}

module "s3_bucket_team_card_photos" {
  source        = "**************:HavenEngineering/tf-s3-bucket?ref=v3.0.1"
  resource_name = "haven-dev-experience-team-card-photos"

}

module "s3_bucket_team_card_physical_card_export" {
  source        = "**************:HavenEngineering/tf-s3-bucket?ref=v3.0.1"
  resource_name = "haven-dev-experience-team-card-physical-card-export"
}
