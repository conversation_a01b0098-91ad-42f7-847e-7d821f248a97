resource "random_password" "service_haven_contact_preferences_bloomreach_jwt_secret" {
  length  = 16
  special = false
}

resource "random_password" "service_haven_contact_preferences_bloomreach_api_key" {
  length  = 16
  special = false
}

# service-haven-contact-preferences
module "secrets_service_haven_contact_preferences" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "service-haven-contact-preferences"
  kubernetes_namespace = "dev-contact-preferences"

  secrets = {
    DATABASE_URL                     = "postgresql://${module.postgres_service_haven_contact_preferences.db_username}:${module.postgres_service_haven_contact_preferences.db_password}@${module.postgres_service_haven_contact_preferences.db_endpoint}/${module.postgres_service_haven_contact_preferences.db_name}?schema=public"
    SNOWFLAKE_PRIVATE_KEY            = trimspace(file("${path.module}/secrets/snowflake-private-key.secret"))
    SNOWFLAKE_PRIVATE_KEY_PASSPHRASE = trimspace(file("${path.module}/secrets/snowflake-passphrase.secret"))
    IDENTITY_API_KEY                 = trimspace(file("${path.module}/secrets/identity-service-api-key.secret"))
    PLOT_API_KEY                     = trimspace(file("${path.module}/secrets/plot-api-key.secret"))
    BLOOMREACH_API_KEY               = random_password.service_haven_contact_preferences_bloomreach_api_key.result
  }
}


# app-haven-contact-preferences
module "secrets_app_haven_contact_preferences" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "app-haven-contact-preferences"
  kubernetes_namespace = "dev-contact-preferences"

  secrets = {
    BLOOMREACH_JWT_SECRET    = random_password.service_haven_contact_preferences_bloomreach_jwt_secret.result
    IDENTITY_API_KEY         = trimspace(file("${path.module}/secrets/identity-service-api-key.secret"))
    UNLEASH_FRONTEND_API_KEY = trimspace(file("${path.module}/secrets/unleash-frontend-api-key.secret"))
  }
}
