data "terraform_remote_state" "holidaysales_land_and_discover" {
  backend = "s3"

  config = {
    bucket  = var.terraform_state_bucket
    key     = "dev/holidaysales-land-and-discover"
    profile = var.terraform_state_profile
    region  = var.terraform_state_region
  }
}

module "sqs_queue_service_haven_contact_preferences_snowflake_update" {
  source                     = "**************:HavenEngineering/tf-sqs-queue?ref=v3.0.0"
  consumer_name              = "service-haven-contact-preferences"
  consumer_purpose           = "insert"
  environment_name           = "dev"
  message_type               = "snowflake-update-uploaded"
  sns_topic_arns             = [module.sns_topic_contact_preferences_snowflake_updates.topic_arn]
  visibility_timeout_seconds = 600
}

module "sqs_queue_service_haven_contact_preferences_cms_update" {
  source                     = "**************:HavenEngineering/tf-sqs-queue?ref=v3.0.0"
  consumer_name              = "service-haven-contact-preferences"
  consumer_purpose           = "insert"
  environment_name           = "dev"
  message_type               = "cms-preferences-consent"
  filter_policy              = "{\"entity\": [{ \"anything-but\": \"QUIZ_LEAD\" }], \"body\": { \"emailAddress\": [{ \"exists\": true }], \"consent\": [true]}}"
  filter_policy_scope        = "MessageBody"
  sns_topic_arns             = [data.terraform_remote_state.holidaysales_land_and_discover.outputs.service_haven_sales_leads_topic_arn]
  visibility_timeout_seconds = 10
}
