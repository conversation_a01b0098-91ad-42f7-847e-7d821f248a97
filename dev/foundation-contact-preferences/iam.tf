# Foundation Contact Preferences Service IAM
data "aws_iam_policy_document" "service_haven_contact_preferences" {
  statement {
    effect    = "Allow"
    actions   = ["s3:ListBucket"]
    resources = [module.s3_bucket_contact_preferences_snowflake_updates.bucket_arn]
  }

  statement {
    effect = "Allow"
    actions = [
      "s3:PutObject",
      "s3:GetObject",
      "s3:GetObjectAttributes",
      "s3:DeleteObject",
    ]
    resources = ["${module.s3_bucket_contact_preferences_snowflake_updates.bucket_arn}/*"]
  }

  statement {
    effect = "Allow"
    actions = [
      "SQS:ChangeMessageVisibility",
      "SQS:DeleteMessage",
      "SQS:ReceiveMessage",
      "SQS:SendMessage"
    ]
    resources = [
      module.sqs_queue_service_haven_contact_preferences_snowflake_update.work_queue_arn,
      module.sqs_queue_service_haven_contact_preferences_snowflake_update.deadletter_queue_arn,
      # any new resources need to be added to /platform-k8s/iam-contact-preferences
    ]
  }
}

# Snowflake Export Policies
data "aws_iam_policy_document" "snowflake_assume_role" {
  statement {
    actions = ["sts:AssumeRole"]

    principals {
      type        = "AWS"
      identifiers = [var.snowflake_user_arn]
    }

    condition {
      test     = "StringEquals"
      variable = "sts:ExternalId"

      values = [
        var.snowflake_external_id
      ]
    }
  }
}

data "aws_iam_policy_document" "snowflake_exports_write_s3_bucket" {
  statement {
    actions = [
      "s3:ListBucket",
      "s3:PutObject",
      "s3:GetObject",
      "s3:GetObjectVersion",
      "s3:DeleteObject",
      "s3:DeleteObjectVersion"
    ]

    resources = [
      "${module.s3_bucket_contact_preferences_snowflake_updates.bucket_arn}",
      "${module.s3_bucket_contact_preferences_snowflake_updates.bucket_arn}/*"
    ]
  }
}

resource "aws_iam_policy" "snowflake_exports_write_s3_bucket" {
  name        = "${var.environment_name}-contact-preferences-snowflake-s3-write"
  description = "Provides required permissions for S3 write access"
  path        = "/"
  policy      = data.aws_iam_policy_document.snowflake_exports_write_s3_bucket.json
}

resource "aws_iam_role" "snowflake_crm_exports" {
  name               = "${var.environment_name}-contact-preferences-snowflake-role"
  description        = "Role for the Snowflake connector"
  path               = "/"
  assume_role_policy = data.aws_iam_policy_document.snowflake_assume_role.json
  managed_policy_arns = [
    aws_iam_policy.snowflake_exports_write_s3_bucket.arn
  ]
}
