module "s3_bucket_bloomreach_import" {
  source        = "**************:HavenEngineering/tf-s3-bucket?ref=v3.0.1"
  resource_name = "haven-dev-foundation-bloomreach-import"
}

module "s3_bucket_lambda_crm_hid_processor" {
  source        = "**************:HavenEngineering/tf-s3-bucket?ref=v3.0.1"
  resource_name = "haven-dev-foundation-lambda-crm-hid-processor"
}
module "s3_bucket_contact_preferences_snowflake_updates" {
  source        = "**************:HavenEngineering/tf-s3-bucket?ref=v3.0.1"
  resource_name = "haven-dev-foundation-contact-preferences-snowflake-updates"
  expiry        = true
  expiry_days   = 7
}

resource "aws_s3_bucket_notification" "s3_bucket_contact_preferences_snowflake_updates_sns_notification" {
  bucket = module.s3_bucket_contact_preferences_snowflake_updates.bucket_name

  topic {
    topic_arn = module.sns_topic_contact_preferences_snowflake_updates.topic_arn
    events    = ["s3:ObjectCreated:*"]
  }
}