locals {
  meta = {
    env           = var.environment_name
    tribe         = "Foundation"
    team          = "Customer Identity"
    product       = "Customer Identity"
    service       = "foundation-contact-preferences"
    terraformrepo = "platform-terraform/dev/foundation-contact-preferences"
    tagversion    = "2.0.0"
  }
}

variable "snowflake_user_arn" {
  type        = string
  description = "The ARN of the IAM user that <PERSON><PERSON><PERSON> will be accessing S3 via"
  default     = "arn:aws:iam::270302263326:user/m5a8-s-iest2078"
}

variable "snowflake_external_id" {
  type        = string
  description = "The external ID of the IAM user that <PERSON><PERSON><PERSON> will be accessing S3 via"
  default     = "BD78472_SFCRole=2_4WTgB2eZOL36qqCAdvePWshDS54="
}