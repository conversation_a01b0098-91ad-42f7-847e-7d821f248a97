module "secrets_common_amplitude" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "amplitude"
  kubernetes_namespace = "common-secrets"

  secrets = {
    for f in fileset(path.module, "secrets/amplitude/*.secret") : trimsuffix(basename(f), ".secret") => trimspace(file(f))
  }
}

module "secrets_common_contentful" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "contentful"
  kubernetes_namespace = "common-secrets"

  secrets = {
    for f in fileset(path.module, "secrets/contentful/*.secret") : trimsuffix(basename(f), ".secret") => trimspace(file(f))
  }
}

module "secrets_common_github" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "github"
  kubernetes_namespace = "common-secrets"

  secrets = {
    for f in fileset(path.module, "secrets/github/*.secret") : trimsuffix(basename(f), ".secret") => trimspace(file(f))
  }
}
