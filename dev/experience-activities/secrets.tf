# resources
data "terraform_remote_state" "shared_playpass" {
  backend = "s3"

  config = {
    bucket  = var.terraform_state_bucket
    key     = "dev/shared-playpass"
    profile = var.terraform_state_profile
    region  = var.terraform_state_region
  }
}

data "terraform_remote_state" "experience_aac" {
  backend = "s3"

  config = {
    bucket  = var.terraform_state_bucket
    key     = "dev/experience-aac"
    profile = var.terraform_state_profile
    region  = var.terraform_state_region
  }
}

data "terraform_remote_state" "ownership-owners" {
  backend = "s3"

  config = {
    bucket  = var.terraform_state_bucket
    key     = "dev/ownership-owners"
    profile = var.terraform_state_profile
    region  = var.terraform_state_region
  }
}

locals {
  shared_playpass_db_url   = data.terraform_remote_state.shared_playpass.outputs.db_url
  owners_arrivals_api_keys = data.terraform_remote_state.ownership-owners.outputs.service_haven_owners_arrivals_api_keys
  arrivals_api_key         = data.terraform_remote_state.experience_aac.outputs.arrivals_api_key
}

resource "random_password" "service_haven_activities_reporting_api_key" {
  length  = 16
  special = false
}

resource "random_password" "service_haven_activities_smartseer_api_key" {
  length  = 16
  special = false
}

resource "random_password" "service_haven_activities_favourites_api_key" {
  length  = 16
  special = false
}

resource "random_password" "service_haven_activities_pricing_admin_api_key" {
  length  = 16
  special = false
}

resource "random_password" "service_haven_activities_pricing_activities_internal_api_key" {
  length  = 16
  special = false
}

resource "random_password" "service_haven_activities_capacity_admin_api_key" {
  length  = 16
  special = false
}

resource "random_password" "service_haven_activities_capacity_activities_internal_api_key" {
  length  = 16
  special = false
}

resource "random_password" "service_haven_activities_acceptance_of_risk_service_admin_api_key" {
  length  = 16
  special = false
}

resource "random_password" "service_haven_activities_acceptance_of_risk_service_activities_internal_api_key" {
  length  = 16
  special = false
}

resource "random_password" "service_haven_activities_zam_gateway_api_key" {
  length  = 16
  special = false
}

resource "random_password" "service_haven_activities_team_gateway_api_key" {
  length  = 16
  special = false
}

resource "random_password" "service_haven_activities_team_gateway_admin_api_key" {
  length  = 16
  special = false
}

resource "random_password" "service_haven_activities_admin_gateway_api_key" {
  length  = 16
  special = false
}

resource "random_password" "service_haven_activities_guest_gateway_api_key" {
  length  = 16
  special = false
}

resource "random_password" "service_haven_activities_service_info_api_key" {
  length  = 16
  special = false
}

resource "random_password" "service_haven_activities_content_api_key" {
  length  = 16
  special = false
}

resource "random_password" "service_haven_activities_admin_gateway_webhook_api_key" {
  length  = 16
  special = false
}

resource "random_password" "service_haven_activities_recommendations_api_key" {
  length  = 16
  special = false
}

module "secrets_service_haven_activities_service_v2" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "service-haven-activities-service-v2"
  kubernetes_namespace = "dev-haven-activities"

  secrets = {
    DB_HOST                              = module.postgres_service_haven_activities_service.db_address
    DB_NAME                              = module.postgres_service_haven_activities_service.db_name
    DB_USER_NAME                         = module.postgres_service_haven_activities_service.db_username
    DB_USER_PASSWORD                     = module.postgres_service_haven_activities_service.db_password
    SHARED_SERVICE_SYNC_WEBHOOK_AUTH_KEY = trimspace(file("${path.module}/secrets-values/dev/shared_service_sync_webhook_auth_key.secret"))
    SHARED_SERVICE_SYNC_CONTENFUL_ID     = trimspace(file("${path.module}/secrets-values/dev/shared_service_sync_contenful_id.secret"))
    ATE_API_KEY                          = trimspace(file("${path.module}/secrets-values/dev/ate_api_key.secret"))
    BOOKING_API_KEY                      = trimspace(file("${path.module}/secrets-values/dev/booking_api_key.secret"))
    CONTENT_API_KEY                      = trimspace(file("${path.module}/secrets-values/dev/content_api_key.secret"))
    API_KEY_CONTENT                      = trimspace(file("${path.module}/secrets-values/dev/api_key_content.secret"))
    CMS_API_KEY                          = trimspace(file("${path.module}/secrets-values/dev/cms_api_key.secret"))
    CAPACITY_API_KEY                     = random_password.service_haven_activities_capacity_activities_internal_api_key.result
    PRICING_API_KEY                      = random_password.service_haven_activities_pricing_activities_internal_api_key.result
    CONTENT_SERVICE_API_KEY              = random_password.service_haven_activities_content_api_key.result
    RECOMMENDATIONS_API_KEY              = random_password.service_haven_activities_recommendations_api_key.result
    MANAGEMENT_API_KEY                   = trimspace(file("${path.module}/secrets-values/dev/management_api_key.secret"))
    NOTIFICATION_SERVICE_API_KEY         = trimspace(file("${path.module}/secrets-values/dev/cello_api_key.secret"))
    BOURNE_API_KEY                       = trimspace(file("${path.module}/secrets-values/dev/bourne_api_key.secret"))
  }
}

module "secrets_service_haven_experience_capacity" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "service-haven-activities-capacity"
  kubernetes_namespace = "dev-haven-activities"

  secrets = {
    DB_USER_NAME                 = module.postgres_service_haven_experience_capacity.db_username
    DB_USER_PASSWORD             = module.postgres_service_haven_experience_capacity.db_password
    DB_HOST                      = module.postgres_service_haven_experience_capacity.db_address
    DB_NAME                      = module.postgres_service_haven_experience_capacity.db_name
    ADMIN_API_KEY                = random_password.service_haven_activities_capacity_admin_api_key.result
    SERVICE_INFO_API_KEY         = random_password.service_haven_activities_service_info_api_key.result
    ACTIVITIES_INTERNAL_API_KEY  = random_password.service_haven_activities_capacity_activities_internal_api_key.result
    NOTIFICATION_SERVICE_API_KEY = trimspace(file("${path.module}/secrets-values/dev/cello_api_key.secret"))
  }
}

module "secrets_service_haven_activities_zam_gateway" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "service-haven-activities-zam-gateway"
  kubernetes_namespace = "dev-haven-activities"

  secrets = {
    API_KEY              = random_password.service_haven_activities_zam_gateway_api_key.result
    SERVICE_INFO_API_KEY = random_password.service_haven_activities_service_info_api_key.result
  }
}

module "secrets_service_haven_activities_team_gateway" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "service-haven-activities-team-gateway"
  kubernetes_namespace = "dev-haven-activities"

  secrets = {
    API_KEY                            = random_password.service_haven_activities_team_gateway_api_key.result
    SERVICE_INFO_API_KEY               = random_password.service_haven_activities_service_info_api_key.result
    CAPACITY_SERVICE_API_KEY           = random_password.service_haven_activities_capacity_activities_internal_api_key.result
    PRICING_SERVICE_PUBLIC_API_KEY     = random_password.service_haven_activities_pricing_activities_internal_api_key.result
    GUEST_GATEWAY_API_KEY              = random_password.service_haven_activities_guest_gateway_api_key.result
    ACTIVITIES_SERVICE_API_KEY         = trimspace(file("${path.module}/secrets-values/dev/content_api_key.secret"))
    BOOKING_SERVICE_API_KEY            = trimspace(file("${path.module}/secrets-values/dev/booking_api_key.secret"))
    SID_AUTH_SECRET                    = trimspace(file("${path.module}/secrets-values/dev/sid_auth_secret.secret"))
    ACTIVITIES_SERVICE_API_KEY         = trimspace(file("${path.module}/secrets-values/dev/activities_api_key.secret"))
    ZETTLE_PAYMENT_CREATED_SIGNING_KEY = trimspace(file("${path.module}/secrets-values/dev/zettle_payment_created_signing_key.secret"))
    ADMIN_API_KEY                      = random_password.service_haven_activities_team_gateway_admin_api_key.result
    ACCEPTANCE_API_KEY                 = random_password.service_haven_activities_acceptance_of_risk_service_activities_internal_api_key.result
  }
}

module "secrets_service_haven_activities_gateway" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "service-haven-activities-gateway"
  kubernetes_namespace = "dev-haven-activities"

  secrets = {
    AUTH_SECRET                = trimspace(file("${path.module}/secrets-values/dev/sid_auth_secret.secret"))
    API_KEY_ACCEPTANCE         = random_password.service_haven_activities_acceptance_of_risk_service_activities_internal_api_key.result
    API_KEY_ACTIVITIES         = trimspace(file("${path.module}/secrets-values/dev/content_api_key.secret"))
    CAPACITY_API_KEY           = random_password.service_haven_activities_capacity_activities_internal_api_key.result
    PRICING_API_KEY            = random_password.service_haven_activities_pricing_activities_internal_api_key.result
    SERVICE_TO_SERVICE_API_KEY = random_password.service_haven_activities_guest_gateway_api_key.result
    BOOKING_API_KEY            = trimspace(file("${path.module}/secrets-values/dev/booking_api_key.secret"))
    FAVOURITES_API_KEY         = random_password.service_haven_activities_favourites_api_key.result
    RECOMMENDATIONS_API_KEY    = random_password.service_haven_activities_recommendations_api_key.result
  }
}

module "secrets_service_haven_activities_admin_gateway" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "service-haven-activities-admin-gateway"
  kubernetes_namespace = "dev-haven-activities"

  secrets = {
    API_KEY                               = random_password.service_haven_activities_admin_gateway_api_key.result
    SERVICE_INFO_API_KEY                  = random_password.service_haven_activities_service_info_api_key.result
    ACTIVITIES_SERVICE_API_KEY            = trimspace(file("${path.module}/secrets-values/dev/content_api_key.secret"))
    ACTIVITIES_SERVICE_MANAGEMENT_API_KEY = trimspace(file("${path.module}/secrets-values/dev/management_api_key.secret"))
    BOOKING_SERVICE_API_KEY               = trimspace(file("${path.module}/secrets-values/dev/booking_api_key.secret"))
    CAPACITY_SERVICE_API_KEY              = random_password.service_haven_activities_capacity_activities_internal_api_key.result
    PRICING_SERVICE_PUBLIC_API_KEY        = random_password.service_haven_activities_pricing_activities_internal_api_key.result
    PRICING_SERVICE_MANAGEMENT_API_KEY    = random_password.service_haven_activities_pricing_admin_api_key.result
    CAPACITY_SERVICE_MANAGEMENT_API_KEY   = random_password.service_haven_activities_capacity_admin_api_key.result
    ACCEPTANCE_API_KEY                    = random_password.service_haven_activities_acceptance_of_risk_service_activities_internal_api_key.result
    ARRIVALS_API_KEY                      = local.arrivals_api_key
    WEBHOOK_API_KEY                       = random_password.service_haven_activities_admin_gateway_webhook_api_key.result
  }
}

module "secrets_service_haven_activities_pricing" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "service-haven-activities-pricing"
  kubernetes_namespace = "dev-haven-activities"

  secrets = {
    DB_USER_NAME                        = module.postgres_service_haven_activities_pricing.db_username
    DB_USER_PASSWORD                    = module.postgres_service_haven_activities_pricing.db_password
    DB_HOST                             = module.postgres_service_haven_activities_pricing.db_address
    DB_NAME                             = module.postgres_service_haven_activities_pricing.db_name
    ADMIN_API_KEY                       = random_password.service_haven_activities_pricing_admin_api_key.result
    SERVICE_INFO_API_KEY                = random_password.service_haven_activities_service_info_api_key.result
    ACTIVITIES_INTERNAL_API_KEY         = random_password.service_haven_activities_pricing_activities_internal_api_key.result
    CAPACITY_SERVICE_API_KEY            = random_password.service_haven_activities_capacity_activities_internal_api_key.result
    CAPACITY_SERVICE_MANAGEMENT_API_KEY = random_password.service_haven_activities_capacity_admin_api_key.result
  }
}

module "secrets_service_haven_activities_analytics" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "service-haven-activities-analytics"
  kubernetes_namespace = "dev-haven-activities"

  secrets = {
    SERVICE_HAVEN_ACTIVITIES_ANALYTICS_DB_URI = "postgresql://${module.postgres_service_haven_experience_analytics.db_username}:${module.postgres_service_haven_experience_analytics.db_password}@${module.postgres_service_haven_experience_analytics.db_address}/${module.postgres_service_haven_experience_analytics.db_name}"
    SERVICE_HAVEN_ACTIVITIES_CAPACITY_DB_URI  = "postgresql://${module.postgres_service_haven_experience_capacity.db_username}:${module.postgres_service_haven_experience_capacity.db_password}@${module.postgres_service_haven_experience_capacity.db_address}/${module.postgres_service_haven_experience_capacity.db_name}"
    SERVICE_HAVEN_ACTIVITIES_PRICING_DB_URI   = "postgresql://${module.postgres_service_haven_activities_pricing.db_username}:${module.postgres_service_haven_activities_pricing.db_password}@${module.postgres_service_haven_activities_pricing.db_address}/${module.postgres_service_haven_activities_pricing.db_name}"
    SERVICE_SHARED_PLAYPASS_DB_URI            = local.shared_playpass_db_url
    // these are DBs setup in the legacy way so we can't reference module outputs directly
    SERVICE_HAVEN_ACTIVITIES_SERVICE_DB_URI         = trimspace(file("${path.module}/secrets-values/dev/service_haven_activities_service_db_uri.secret"))
    SERVICE_HAVEN_ACTIVITIES_BOOKING_DB_URI         = trimspace(file("${path.module}/secrets-values/dev/service_haven_activities_booking_db_uri.secret"))
    SERVICE_HAVEN_ACTIVITIES_SINGLE_IDENTITY_DB_URI = trimspace(file("${path.module}/secrets-values/dev/service_haven_activities_single_identity_db_uri.secret"))
    ZAM_RO_DB_URI                                   = trimspace(file("${path.module}/secrets-values/dev/zam_ro_db_uri.secret"))
  }
}

module "secrets_service_haven_experience_booking" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "service-haven-activities-booking"
  kubernetes_namespace = "dev-haven-activities"

  secrets = {
    ZETTLE_AUTH0_CLIENT_ID               = trimspace(file("${path.module}/secrets-values/dev/zettle_auth_client_id.secret"))
    ZETTLE_AUTH0_ASSERTION               = trimspace(file("${path.module}/secrets-values/dev/zettle_auth_assertion.secret"))
    OWNERS_ARRIVALS_API_KEY              = local.owners_arrivals_api_keys["service-haven-experience-activities-booking"]
    ACCEPTANCE_API_KEY                   = random_password.service_haven_activities_acceptance_of_risk_service_activities_internal_api_key.result
    CONTENT_API_KEY                      = random_password.service_haven_activities_content_api_key.result
    CONTENTFUL_DELIVERY_API_ACCESS_TOKEN = trimspace(file("${path.module}/secrets-values/dev/contentful-delivery-api-access-token.secret"))
  }
}

module "secrets_service_haven_activities_acceptance_of_risk_service" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "service-haven-activities-acceptance-of-risk-service"
  kubernetes_namespace = "dev-haven-activities"

  secrets = {
    DB_USER_NAME                = module.postgres_service_haven_activities_acceptance_of_risk_service.db_username
    DB_USER_PASSWORD            = module.postgres_service_haven_activities_acceptance_of_risk_service.db_password
    DB_HOST                     = module.postgres_service_haven_activities_acceptance_of_risk_service.db_address
    DB_NAME                     = module.postgres_service_haven_activities_acceptance_of_risk_service.db_name
    ADMIN_API_KEY               = random_password.service_haven_activities_acceptance_of_risk_service_admin_api_key.result
    SERVICE_INFO_API_KEY        = random_password.service_haven_activities_service_info_api_key.result
    ACTIVITIES_INTERNAL_API_KEY = random_password.service_haven_activities_acceptance_of_risk_service_activities_internal_api_key.result
  }
}

module "secrets_app_haven_activities_portal" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "app-haven-activities-portal"
  kubernetes_namespace = "dev-haven-activities"

  secrets = {
    CONTENTFUL_DELIVERY_API_ACCESS_TOKEN = trimspace(file("${path.module}/secrets-values/dev/contentful-delivery-api-access-token.secret"))
  }
}

module "secrets_service_haven_activities_reporting" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "service-haven-activities-reporting"
  kubernetes_namespace = "dev-haven-activities"

  secrets = {
    API_KEY                 = random_password.service_haven_activities_reporting_api_key.result
    POSTGRES_URI            = trimspace(file("${path.module}/secrets-values/dev/dead_integrations_db_uri.secret"))
    CONTENT_SERVICE_API_KEY = random_password.service_haven_activities_content_api_key.result
  }
}

module "secrets_service_haven_activities_smartseer" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "service-haven-activities-smartseer"
  kubernetes_namespace = "dev-haven-activities"

  secrets = {
    API_KEY                 = random_password.service_haven_activities_smartseer_api_key.result
    POSTGRES_URI            = trimspace(file("${path.module}/secrets-values/dev/dead_integrations_db_uri.secret"))
    CONTENT_SERVICE_API_KEY = random_password.service_haven_activities_content_api_key.result
  }
}

module "secrets_service_haven_activities_favourites" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "service-haven-activities-favourites"
  kubernetes_namespace = "dev-haven-activities"

  secrets = {
    DB_USER_NAME     = module.postgres_service_haven_activities_favourites.db_username
    DB_USER_PASSWORD = module.postgres_service_haven_activities_favourites.db_password
    DB_HOST          = module.postgres_service_haven_activities_favourites.db_address
    DB_NAME          = module.postgres_service_haven_activities_favourites.db_name
    API_KEY          = random_password.service_haven_activities_favourites_api_key.result
  }
}

module "secrets_service_haven_activities_content" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "service-haven-activities-content"
  kubernetes_namespace = "dev-haven-activities"

  secrets = {
    API_KEY                              = random_password.service_haven_activities_content_api_key.result
    CONTENTFUL_DELIVERY_API_ACCESS_TOKEN = trimspace(file("${path.module}/secrets-values/dev/contentful-delivery-api-access-token.secret"))
    CONTENTFUL_MANAGEMENT_API_KEY        = trimspace(file("${path.module}/secrets-values/dev/contentful-management-api-cma-token.secret"))
  }
}

module "secrets_service_haven_activities_recommendations" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "service-haven-activities-recommendations"
  kubernetes_namespace = "dev-haven-activities"

  secrets = {
    API_KEY            = random_password.service_haven_activities_recommendations_api_key.result
    API_KEY_ACTIVITIES = trimspace(file("${path.module}/secrets-values/dev/content_api_key.secret"))
  }
}

output "team_gateway_api_key" {
  description = "Activities Team Gateway API key"
  value       = random_password.service_haven_activities_team_gateway_api_key.result
  sensitive   = true
}

output "sid_auth_secret" {
  description = "Activities SID API key"
  value       = trimspace(file("${path.module}/secrets-values/dev/sid_auth_secret.secret"))
  sensitive   = true
}

output "activities_service_management_api_key" {
  description = "Activities Service Management API key"
  value       = trimspace(file("${path.module}/secrets-values/dev/management_api_key.secret"))
  sensitive   = true
}

output "activities_service_api_key" {
  description = "Activities Service API key"
  value       = trimspace(file("${path.module}/secrets-values/dev/activities_api_key.secret"))
  sensitive   = true
}

output "activities_booking_service_api_key" {
  description = "Activities Booking Service API key"
  value       = trimspace(file("${path.module}/secrets-values/dev/booking_api_key.secret"))
  sensitive   = true
}

output "activities_capacity_api_key" {
  description = "Activities Capacity API key"
  value       = random_password.service_haven_activities_capacity_activities_internal_api_key.result
  sensitive   = true
}

output "activities_pricing_api_key" {
  description = "Activities Pricing API key"
  value       = random_password.service_haven_activities_pricing_activities_internal_api_key.result
  sensitive   = true
}

output "activities_guest_gateway_api_key" {
  description = "Activities Guest Gateway API key"
  value       = random_password.service_haven_activities_guest_gateway_api_key.result
  sensitive   = true
}

output "activities_acceptance_of_risk_api_key" {
  description = "Activities Acceptance of Risk API key"
  value       = random_password.service_haven_activities_acceptance_of_risk_service_activities_internal_api_key.result
  sensitive   = true
}

output "activities_zettle_payment_signing_key" {
  description = "Activities Zettle Payment Signing key"
  value       = trimspace(file("${path.module}/secrets-values/dev/zettle_payment_created_signing_key.secret"))
  sensitive   = true
}

output "activities_content_api_key" {
  description = "Activities Content Service API key"
  value       = random_password.service_haven_activities_content_api_key.result
  sensitive   = true
}

output "activities_recommendations_api_key" {
  description = "Activities Recommendations API key"
  value       = random_password.service_haven_activities_recommendations_api_key.result
  sensitive   = true
}
