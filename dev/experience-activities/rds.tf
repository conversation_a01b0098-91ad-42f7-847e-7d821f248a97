data "aws_security_group" "dms_sg" {
  vpc_id = data.aws_vpc.main.id

  filter {
    name   = "group-name"
    values = ["dev-experience-shared-dms"]
  }
}

module "postgres_service_haven_experience_analytics" {
  source             = "**************:HavenEngineering/tf-rds-instance?ref=v4.2.0"
  resource_name      = "dev-experience-analytics"
  instance_type      = "db.t4g.small"
  source_cidr_blocks = local.tailscale_network_account_cidrs
  multi_az           = false

  parameters_family             = "postgres15"
  aws_db_parameter_group_suffix = "-v15"
  engine_version                = "15.12"
  parameters = [
    {
      name  = "rds.force_ssl",
      value = "0",
  }]

  cloudwatch_actions = {
    warning  = data.aws_sns_topic.rds_alerts.arn
    critical = data.aws_sns_topic.rds_alerts.arn
  }

  additional_instance_tags = {
    "StartInstance" : base64encode("cron(0 2 ? * WED *)|cron(50 5 ? * MON-FRI *)")
    "StopInstance" : base64encode("cron(0 3 ? * WED *)|cron(05 20 ? * MON-FRI *)")
    "StopOverride" : "dd.mm.yyyy"
  }
}

module "postgres_service_haven_experience_capacity" {
  source                        = "**************:HavenEngineering/tf-rds-instance?ref=v4.2.0"
  resource_name                 = "dev-experience-capacity"
  aws_db_parameter_group_suffix = "-v15"

  instance_type     = "db.t4g.small"
  engine_version    = "15.12"
  parameters_family = "postgres15"
  multi_az          = false

  source_cidr_blocks = local.dev_db_default_allowed_cidrs
  security_group_ids = [data.aws_security_group.dms_replication_instances.id]

  parameters = [
    {
      name  = "rds.logical_replication",
      value = "1",
    },
    {
      name  = "rds.force_ssl",
      value = "0",
    }
  ]

  additional_instance_tags = {
    "blg:dms" : "experience"
    "StartInstance" : base64encode("cron(0 2 ? * WED *)|cron(50 5 ? * MON-FRI *)")
    "StopInstance" : base64encode("cron(0 3 ? * WED *)|cron(05 20 ? * MON-FRI *)")
    "StopOverride" : "dd.mm.yyyy"
  }

  cloudwatch_actions = {
    warning  = data.aws_sns_topic.rds_alerts.arn
    critical = data.aws_sns_topic.rds_alerts.arn
  }
}

module "postgres_service_haven_activities_pricing" {
  source                        = "**************:HavenEngineering/tf-rds-instance?ref=v4.2.0"
  resource_name                 = "dev-experience-pricing"
  aws_db_parameter_group_suffix = "-v15"


  instance_type     = "db.t4g.small"
  engine_version    = "15.12"
  parameters_family = "postgres15"
  multi_az          = false
  storage_size      = "75"

  source_cidr_blocks = local.dev_db_default_allowed_cidrs
  security_group_ids = [data.aws_security_group.dms_replication_instances.id]

  parameters = [
    {
      name  = "rds.logical_replication",
      value = "1",
    },
    {
      name  = "rds.force_ssl",
      value = "0",
    }
  ]

  additional_instance_tags = {
    "blg:dms" : "experience"
    "StartInstance" : base64encode("cron(0 2 ? * WED *)|cron(50 5 ? * MON-FRI *)")
    "StopInstance" : base64encode("cron(0 3 ? * WED *)|cron(05 20 ? * MON-FRI *)")
    "StopOverride" : "dd.mm.yyyy"
  }

  cloudwatch_actions = {
    warning  = data.aws_sns_topic.rds_alerts.arn
    critical = data.aws_sns_topic.rds_alerts.arn
  }
}

module "postgres_service_haven_activities_acceptance_of_risk_service" {
  source        = "**************:HavenEngineering/tf-rds-instance?ref=v4.2.0"
  resource_name = "dev-experience-acceptanceofrisk"

  aws_db_parameter_group_suffix = "-v15"

  instance_type     = "db.t4g.small"
  engine_version    = "15.12"
  parameters_family = "postgres15"
  multi_az          = false
  storage_size      = "75"

  source_cidr_blocks = local.dev_db_default_allowed_cidrs
  security_group_ids = [data.aws_security_group.dms_replication_instances.id]

  parameters = [
    {
      name  = "rds.logical_replication",
      value = "1",
    },
    {
      name  = "rds.force_ssl",
      value = "0",
    }
  ]

  additional_instance_tags = {
    "blg:dms" : "experience"
    "StartInstance" : base64encode("cron(0 2 ? * WED *)|cron(50 5 ? * MON-FRI *)")
    "StopInstance" : base64encode("cron(0 3 ? * WED *)|cron(05 20 ? * MON-FRI *)")
    "StopOverride" : "dd.mm.yyyy"
  }

  cloudwatch_actions = {
    warning  = data.aws_sns_topic.rds_alerts.arn
    critical = data.aws_sns_topic.rds_alerts.arn
  }
}

module "postgres_service_haven_activities_service" {
  source        = "**************:HavenEngineering/tf-rds-instance?ref=v4.2.0"
  resource_name = "dev-experience-activities"

  instance_type      = "db.t4g.small"
  source_cidr_blocks = local.dev_db_default_allowed_cidrs
  security_group_ids = [data.aws_security_group.dms_replication_instances.id]
  multi_az           = false
  storage_size       = "500"
  storage_iops       = "12000"
  storage_throughput = "500"

  parameters_family             = "postgres15"
  aws_db_parameter_group_suffix = "-v15"
  engine_version                = "15.12"
  parameters = [
    {
      name  = "rds.logical_replication",
      value = "1",
    },
    {
      name  = "rds.force_ssl",
      value = "0",
    }
  ]

  cloudwatch_actions = {
    warning  = data.aws_sns_topic.rds_alerts.arn
    critical = data.aws_sns_topic.rds_alerts.arn
  }

  additional_instance_tags = {
    "StartInstance" : base64encode("cron(0 2 ? * WED *)|cron(50 5 ? * MON-FRI *)")
    "StopInstance" : base64encode("cron(0 3 ? * WED *)|cron(05 20 ? * MON-FRI *)")
    "StopOverride" : "dd.mm.yyyy"
  }
}

module "postgres_service_haven_activities_booking" {
  source        = "**************:HavenEngineering/tf-rds-instance?ref=v4.2.0"
  resource_name = "dev-experience-activities-booking"

  instance_type      = "db.t4g.small"
  source_cidr_blocks = local.dev_db_default_allowed_cidrs
  security_group_ids = [data.aws_security_group.dms_replication_instances.id]
  multi_az           = false
  storage_size       = "110"

  parameters_family             = "postgres15"
  aws_db_parameter_group_suffix = "-v15"
  engine_version                = "15.12"
  parameters = [
    {
      name  = "rds.logical_replication",
      value = "1",
    },
    {
      name  = "rds.force_ssl",
      value = "0",
  }]

  cloudwatch_actions = {
    warning  = data.aws_sns_topic.rds_alerts.arn
    critical = data.aws_sns_topic.rds_alerts.arn
  }

  additional_instance_tags = {
    "StartInstance" : base64encode("cron(0 2 ? * WED *)|cron(50 5 ? * MON-FRI *)")
    "StopInstance" : base64encode("cron(0 3 ? * WED *)|cron(05 20 ? * MON-FRI *)")
    "StopOverride" : "dd.mm.yyyy"
  }
}

module "postgres_service_haven_activities_payments" {
  source        = "**************:HavenEngineering/tf-rds-instance?ref=v4.2.0"
  resource_name = "dev-experience-activities-payments"

  instance_type      = "db.t4g.small"
  source_cidr_blocks = local.dev_db_default_allowed_cidrs
  security_group_ids = [data.aws_security_group.dms_replication_instances.id]
  multi_az           = false
  storage_size       = "100"

  parameters_family             = "postgres15"
  aws_db_parameter_group_suffix = "-v15"
  engine_version                = "15.12"
  parameters = [
    {
      name  = "rds.logical_replication",
      value = "1",
    },
    {
      name  = "rds.force_ssl",
      value = "0",
  }]

  cloudwatch_actions = {
    warning  = data.aws_sns_topic.rds_alerts.arn
    critical = data.aws_sns_topic.rds_alerts.arn
  }

  additional_instance_tags = {
    "StartInstance" : base64encode("cron(0 2 ? * WED *)|cron(50 5 ? * MON-FRI *)")
    "StopInstance" : base64encode("cron(0 3 ? * WED *)|cron(05 20 ? * MON-FRI *)")
    "StopOverride" : "dd.mm.yyyy"
  }
}

module "postgres_service_haven_activities_favourites" {
  source        = "**************:HavenEngineering/tf-rds-instance?ref=v4.2.0"
  resource_name = "dev-experience-activities-favourites"

  instance_type      = "db.t4g.small"
  source_cidr_blocks = local.dev_db_default_allowed_cidrs
  security_group_ids = [data.aws_security_group.dms_replication_instances.id]
  multi_az           = false
  storage_size       = "100"

  parameters_family             = "postgres15"
  aws_db_parameter_group_suffix = "-v15"
  engine_version                = "15.12"
  parameters = [
    {
      name  = "rds.logical_replication",
      value = "1",
    },
    {
      name  = "rds.force_ssl",
      value = "0",
  }]

  additional_instance_tags = {
    "blg:dms" : "experience"
    "StartInstance" : base64encode("cron(0 2 ? * WED *)|cron(50 5 ? * MON-FRI *)")
    "StopInstance" : base64encode("cron(0 3 ? * WED *)|cron(05 20 ? * MON-FRI *)")
    "StopOverride" : "dd.mm.yyyy"
  }

  cloudwatch_actions = {
    warning  = data.aws_sns_topic.rds_alerts.arn
    critical = data.aws_sns_topic.rds_alerts.arn
  }
}
