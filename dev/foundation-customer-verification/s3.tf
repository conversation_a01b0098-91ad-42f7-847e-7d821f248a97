module "s3_bucket_customer_verification" {
  source        = "**************:HavenEngineering/tf-s3-bucket?ref=v3.0.1" #encryption at rest already applied in this module
  resource_name = "dev-customer-haven-customer-verification-s3"
  acl           = "private"

  block_public_access = true
  versioning          = true
  expiry              = true
  expiry_days         = 90
  intelligent_tiering = true
}
