module "secrets_service_haven_customer_verification" {
  source = "**************:HavenEngineering/tf-secrets-manager-secrets?ref=v3.0.0"

  kubernetes_name      = "service-haven-customer-verification"
  kubernetes_namespace = "dev-customer-verification"

  secrets = {
    DATABASE_URL = "postgresql://${module.rds_postgres_customer_verification.db_username}:${module.rds_postgres_customer_verification.db_password}@${module.rds_postgres_customer_verification.db_endpoint}/${module.rds_postgres_customer_verification.db_name}?schema=public"
  }
}