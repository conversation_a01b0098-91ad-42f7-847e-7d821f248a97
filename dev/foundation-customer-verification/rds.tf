module "rds_postgres_customer_verification" {
  source        = "**************:HavenEngineering/tf-rds-instance?ref=v4.2.0"
  resource_name = "dev-customer-haven-customer-verification-rds"

  engine_version     = "16.8"
  instance_type      = "db.t4g.small"
  parameters_family  = "postgres16"
  source_cidr_blocks = local.dev_db_default_allowed_cidrs

  multi_az                  = false
  disable_cloudwatch_alarms = true

  cloudwatch_actions = {
    warning  = ""
    critical = ""
  }
}
