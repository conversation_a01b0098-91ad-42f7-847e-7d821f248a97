/*
 * This is a collection of configuration, variables, locals and datasources that
 * are commonly used in all modules. Each module should include a symlink to
 * this file: ln -sf ../_environment.tf
 *
 */
terraform {
  backend "s3" {}

  # This enforces the version of Terraform that is required to run this configuration.
  # This version matches the version installed in the Docker container referenced in: scripts/tf
  required_version = "1.3.9"

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.88.0"
    }
    pagerduty = {
      source  = "pagerduty/pagerduty"
      version = "~> 3.14.0"
    }
    datadog = {
      source = "DataDog/datadog"
    }
  }
}

provider "aws" {
  profile = var.aws_profile
  region  = var.aws_region

  # Here we set a number of global tags that have either static values or values
  # that modules should not override.
  # See https://github.com/HavenEngineering/haven-terraform/wiki/Tagging
  default_tags {
    tags = {
      "blg:account" = "crushftpdev"
      "blg:service" = try(regex("[^/]+$", "${path.cwd}"), "no-path")
    }
  }
}

variable "aws_account_ids" {
  type        = map(string)
  description = "Map of the various AWS account IDs"
}

variable "aws_environment_vpc_cidrs" {
  type        = map(string)
  description = "Map of the various environments' AWS VPC CIDRs"
}

variable "aws_profile" {
  type        = string
  description = "AWS profile to use with the AWS provider"
}

variable "aws_region" {
  type        = string
  description = "AWS region to configure the AWS provider with"
  default     = "eu-west-1"
}

variable "module_name" {
  type        = string
  description = "The name of this module, derived from the path"
}

variable "environment_name" {
  type        = string
  description = "The name of this environment"
}

variable "terraform_state_bucket" {
  type        = string
  description = "The name of the remote terraform state bucket"
}

variable "terraform_state_profile" {
  type        = string
  description = "The name of the AWS profile to use for accessing the terraform state bucket"
}

variable "terraform_state_region" {
  type        = string
  description = "The AWS region where the terraform state bucket exists"
}

data "aws_vpc" "platform_network" {
  tags = {
    Name = "platform-network"
  }
}

data "aws_subnets" "platform_network_public" {
  filter {
    name   = "vpc-id"
    values = [data.aws_vpc.platform_network.id]
  }

  filter {
    name = "tag:Name"
    values = [
      "platform-network-public-eu-west-1a"
    ]
  }
}

data "aws_subnet" "platform_network_public" {
  for_each = toset(data.aws_subnets.platform_network_public.ids)

  id = each.value
}

data "aws_subnets" "platform_network_private" {
  filter {
    name   = "vpc-id"
    values = [data.aws_vpc.platform_network.id]
  }

  filter {
    name = "tag:Name"
    values = [
      "platform-network-private-eu-west-1a",
      "platform-network-private-eu-west-1b",
      "platform-network-private-eu-west-1c",
    ]
  }
}

data "aws_subnet" "platform_network_private" {
  for_each = toset(data.aws_subnets.platform_network_private.ids)

  id = each.value
}

/*
 * Commonly used datasources.
 *
 */
data "aws_region" "current" {}

data "aws_caller_identity" "current" {}
