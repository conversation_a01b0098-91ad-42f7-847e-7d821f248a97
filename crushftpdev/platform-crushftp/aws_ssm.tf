data "aws_ssm_parameter" "domain_username" {
  name = "/infra/ec2/crushftp-dev/username"
}

data "aws_ssm_parameter" "domain_password" {
  name = "/infra/ec2/crushftp-dev/password"
}




resource "aws_ssm_document" "crushftp_windows_setup" {
  name          = "${local.resource_name}-domain-setup"
  document_type = "Command"

  content = jsonencode({
    schemaVersion = "2.2"
    description   = "Prepare CrushFTP Windows Server"
    mainSteps = [
      {
        action = "aws:runPowerShellScript"
        name   = "runPowerShellJoinADDomain"
        precondition = {
          StringEquals = ["platformType", "Windows"]
        }
        inputs = {
          runCommand = [
            "$username = (Get-SSMParameterValue -Name  ${data.aws_ssm_parameter.domain_username.name} -WithDecryption $True).Parameters[0].Value",
            "$password = (Get-SSMParameterValue -Name ${data.aws_ssm_parameter.domain_password.name} -WithDecryption $True).Parameters[0].Value | ConvertTo-SecureString -asPlainText -Force",
            "$credential = New-Object System.Management.Automation.PSCredential($username,$password)",
            "if ($env:UserDomain -eq \"WORKGROUP\") {",
            "    Write-Host \"Joining the domain...\"",
            "    Add-Computer -Credential $credential -OUPath \"${var.crushftp_admin_ou}\" -ComputerName $env:ComputerName -Domain ${var.adirectory_name} -Restart",
            "    Write-Host \"Rebooting to take effect...\"",
            "    exit 3010",
            "} else {",
            "    Write-Host \"$env:ComputerName already domain member.\"",
            "}"
          ]
        }
      },
      {
        action = "aws:runPowerShellScript",
        name   = "runPowerShellInstallRSATAD",
        precondition = {
          StringEquals = ["platformType", "Windows"]
        }
        inputs = {
          runCommand = [
            "if (-not (Get-WindowsFeature RSAT-AD-PowerShell).Installed) {",
            "    Write-Host \"RSAT-AD-PowerShell not found, installing.\"",
            "    Install-WindowsFeature RSAT-AD-PowerShell",
            "} else {",
            "    Write-Host \"RSAT-AD-PowerShell is installed.\"",
            "}"
          ]
        }
      },
      {
        action = "aws:runPowerShellScript",
        name   = "runPowerShellSetupShutdownUnjoin",
        precondition = {
          StringEquals = ["platformType", "Windows"]
        }
        inputs = {
          runCommand = [
            "Write-Host \"Setting up Shutdown Domain UnJoin...\"",
            "$GPRootPath = \"C:\\Windows\\System32\\GroupPolicy\"",
            "$GPMcnPath = \"C:\\Windows\\System32\\GroupPolicy\\Machine\"",
            "$GPScrPath = \"C:\\Windows\\System32\\GroupPolicy\\Machine\\Scripts\"",
            "$GPSShdPath = \"C:\\Windows\\System32\\GroupPolicy\\Machine\\Scripts\\Shutdown\"",
            "$RegFilePath = \"C:\\ProgramData\\Amazon\\EC2Launch\"",
            "$ScriptFile = [System.IO.Path]::Combine($GPSShdPath, \"Shutdown-UnJoin.ps1\")",
            "$RegistryFile = [System.IO.Path]::Combine($RegFilePath, \"OnShutdown.reg\")",
            "$ScriptBody =",
            "@(",
            "    '$username = (Get-SSMParameterValue -Name ${data.aws_ssm_parameter.domain_username.name} -WithDecryption $True).Parameters[0].Value',",
            "    '$password = (Get-SSMParameterValue -Name ${data.aws_ssm_parameter.domain_password.name} -WithDecryption $True).Parameters[0].Value | ConvertTo-SecureString -asPlainText -Force',",
            "    '$credential = New-Object System.Management.Automation.PSCredential($username,$password)',",
            "    'import-module ActiveDirectory',",
            "    '$DCHostName = (Get-ADDomainController -Discover).HostName',",
            "    'Remove-Computer -WorkgroupName \"WORKGROUP\" -UnjoinDomainCredential $credential -Force -Confirm:$false ',",
            "    'Remove-ADComputer -Identity $env:ComputerName -Credential $credential -Server \"$DCHostName\" -Confirm:$false '",
            ")",
            "$RegistryScript =",
            "@(",
            "    'Windows Registry Editor Version 5.00',",
            "    '[HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Group Policy\\Scripts]',",
            "    '[HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Group Policy\\Scripts\\Shutdown]',",
            "    '[HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Group Policy\\Scripts\\Shutdown\\0]',",
            "    '\"GPO-ID\"=\"LocalGPO\"',",
            "    '\"SOM-ID\"=\"Local\"',",
            "    '\"FileSysPath\"=\"C:\\\\Windows\\\\System32\\\\GroupPolicy\\\\Machine\"',",
            "    '\"DisplayName\"=\"Local Group Policy\"',",
            "    '\"GPOName\"=\"Local Group Policy\"',",
            "    '\"PSScriptOrder\"=dword:00000001',",
            "    '[HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Group Policy\\Scripts\\Shutdown\\0\\0]',",
            "    '\"Script\"=\"Shutdown-UnJoin.ps1\"',",
            "    '\"Parameters\"=\"\"',",
            "    '\"IsPowershell\"=dword:00000001',",
            "    '\"ExecTime\"=hex(b):00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00',",
            "    '[HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Group Policy\\Scripts\\Startup]',",
            "    '[HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Group Policy\\State\\Machine\\Scripts]',",
            "    '[HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Group Policy\\State\\Machine\\Scripts\\Shutdown]',",
            "    '[HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Group Policy\\State\\Machine\\Scripts\\Shutdown\\0]',",
            "    '\"GPO-ID\"=\"LocalGPO\"',",
            "    '\"SOM-ID\"=\"Local\"',",
            "    '\"FileSysPath\"=\"C:\\\\Windows\\\\System32\\\\GroupPolicy\\\\Machine\"',",
            "    '\"DisplayName\"=\"Local Group Policy\"',",
            "    '\"GPOName\"=\"Local Group Policy\"',",
            "    '\"PSScriptOrder\"=dword:00000001',",
            "    '[HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Group Policy\\State\\Machine\\Scripts\\Shutdown\\0\\0]',",
            "    '\"Script\"=\"Shutdown-UnJoin.ps1\"',",
            "    '\"Parameters\"=\"\"',",
            "    '\"ExecTime\"=hex(b):00,00,00,00,00,00,00,00,00,00,00,00,00,00,00,00',",
            "    '[HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Group Policy\\State\\Machine\\Scripts\\Startup]'",
            ")",
            "if (!(Test-Path -Path $GPRootPath -pathType container)) { New-Item -ItemType directory -Path $GPRootPath }",
            "if (!(Test-Path -Path $GPMcnPath -pathType container)) { New-Item -ItemType directory -Path $GPMcnPath }",
            "if (!(Test-Path -Path $GPScrPath -pathType container)) { New-Item -ItemType directory -Path $GPScrPath }",
            "if (!(Test-Path -Path $GPSShdPath -pathType container)) { New-Item -ItemType directory -Path $GPSShdPath }",
            "Set-Content $ScriptFile -Value $ScriptBody",
            "Set-Content $RegistryFile -Value $RegistryScript",
            "regedit.exe /S \"$RegistryFile\"",
            "Write-Host \"Shutdown Domain UnJoin Complete.\""
          ]
        }
      }
    ]
  })

  tags = local.resource_tags
}

resource "aws_ssm_document" "crushftp_windows_setup_with_wait" {
  name          = "${local.resource_name}-domain-setup-with-wait"
  document_type = "Command"

  content = jsonencode({
    schemaVersion = "2.2"
    description   = "Wait between steps for CrushFTP Windows Server setup"
    mainSteps = [
      {
        action = "aws:runPowerShellScript"
        name   = "runPowerShellWait"
        inputs = {
          runCommand = [
            "Write-Host \"Wait between steps...\"",
            "Start-Sleep -Seconds 120", # Wait for 2 minutes
            "Write-Host \"Wait completed. Proceeding with the next steps...\""
          ]
        }
      }
    ]
  })

  tags = local.resource_tags
}


resource "aws_ssm_document" "crushftp_windows_setup2" {
  name          = "${local.resource_name}-domain-setup-part2"
  document_type = "Command"

  content = jsonencode({
    schemaVersion = "2.2"
    description   = "Prepare CrushFTP Windows Server - Part 2"
    mainSteps = [
      {
        action = "aws:runPowerShellScript"
        name   = "runPowerShellRenameServer"
        precondition = {
          StringEquals = ["platformType", "Windows"]
        }
        inputs = {
          runCommand = [
            "$username = (Get-SSMParameterValue -Name  ${data.aws_ssm_parameter.domain_username.name} -WithDecryption $True).Parameters[0].Value",
            "$password = (Get-SSMParameterValue -Name ${data.aws_ssm_parameter.domain_password.name} -WithDecryption $True).Parameters[0].Value | ConvertTo-SecureString -asPlainText -Force",
            "$credential = New-Object System.Management.Automation.PSCredential($username,$password)",
            "$newComputerName = \"${var.new_hostname}\"",
            "if ($env:ComputerName -ne $newComputerName) {",
            "    Write-Host \"Renaming the server to $newComputerName...\"",
            "    Rename-Computer -NewName $newComputerName -DomainCredential $credential -Force",
            "    Write-Host \"Server renamed successfully. Rebooting to apply changes...\"",
            "    Restart-Computer -Force",
            "} else {",
            "    Write-Host \"Server name is already $newComputerName.\"",
            "}"
          ]
        }
      }
    ]
  })
  tags = local.resource_tags
}

resource "aws_ssm_association" "crushftp_windows_part1" {
  name = aws_ssm_document.crushftp_windows_setup.name

  targets {
    key    = "tag:CrushFTPDevAdDomainJoin"
    values = ["true"]
  }
}

resource "aws_ssm_association" "crushftp_windows_part2" {
  name = aws_ssm_document.crushftp_windows_setup2.name

  targets {
    key    = "tag:CrushFTPDevAdDomainJoin"
    values = ["true"]
  }

  depends_on = [aws_ssm_association.crushftp_windows_part1]
}