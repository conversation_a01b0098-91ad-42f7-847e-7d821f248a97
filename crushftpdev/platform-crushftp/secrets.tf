locals {

  secrets = {
    username = trimspace(file("${path.module}/secrets/domain-user.secret"))
    password = trimspace(file("${path.module}/secrets/domain-user-password.secret"))
  }
}

resource "aws_ssm_parameter" "crushftp_dev" {

  for_each = local.secrets

  name  = "/infra/ec2/${local.resource_name}/${each.key}"
  type  = "SecureString"
  value = sensitive(each.value)

  tags = local.resource_tags
}


