resource "aws_kms_key" "default" {
  description             = "${local.resource_name}-default-kms-key"
  deletion_window_in_days = 30
  enable_key_rotation     = true

  tags = local.resource_tags
}

resource "aws_kms_alias" "default" {
  name          = "alias/${local.resource_name}-default"
  target_key_id = aws_kms_key.default.key_id
}


resource "aws_kms_key" "crushftp" {
  description             = "${local.resource_name}-kms-key"
  deletion_window_in_days = 30
  enable_key_rotation     = true

  tags = local.resource_tags
}

resource "aws_kms_alias" "crushftp" {
  name          = "alias/${local.resource_name}"
  target_key_id = aws_kms_key.crushftp.key_id
}
