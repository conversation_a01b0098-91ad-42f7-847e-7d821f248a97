resource "tls_private_key" "aws_rsa_key" {
  algorithm = "RSA"
  rsa_bits  = 4096
}

resource "aws_key_pair" "crushftp_key_pair" {
  key_name   = "crushftp-dev-key"
  public_key = tls_private_key.aws_rsa_key.public_key_openssh
}

# resource "local_file" "ssh_key" {
#   filename = "${aws_key_pair.crushftp_key_pair.key_name}.pem"
#   content = tls_private_key.aws_rsa_key.private_key_pem
#   file_permission = "0400"
# }

data "aws_ami" "windows" {
  most_recent = true

  filter {
    name   = "owner-alias"
    values = ["amazon"]
  }

  filter {
    name   = "name"
    values = ["Windows_Server-2025-English-Full-Base-*"]
  }
  filter {
    name   = "root-device-type"
    values = ["ebs"]
  }
}



module "autoscaling_group_crushftp" {
  source  = "terraform-aws-modules/autoscaling/aws"
  version = "8.0.0"

  # Autoscaling group
  name                            = local.resource_name
  use_name_prefix                 = false
  instance_name                   = local.resource_name
  ignore_desired_capacity_changes = true


  min_size         = 1
  max_size         = 1
  desired_capacity = 1

  health_check_type   = "EC2"
  vpc_zone_identifier = data.aws_subnets.platform_network_private.ids
  key_name            = aws_key_pair.crushftp_key_pair.key_name

  traffic_source_attachments = {
    nlb-rdp = {
      traffic_source_identifier = module.nlb_crushftp.target_groups["rdp"].arn
      traffic_source_type       = "elbv2"
    }
    nlb-winrm = {
      traffic_source_identifier = module.nlb_crushftp.target_groups["winrm"].arn
      traffic_source_type       = "elbv2"
    }
  }

  # Launch template
  launch_template_name        = local.resource_name
  update_default_version      = true
  image_id                    = data.aws_ami.windows.id
  instance_type               = "t3.medium"
  user_data                   = base64encode(file("${path.module}/scripts/enable-winrm.ps1"))
  create_iam_instance_profile = true
  iam_role_name               = local.resource_name
  iam_role_path               = "/ec2/"
  iam_role_description        = "IAM Role for ${local.resource_name}"
  iam_role_policies = {
    SessionManager                  = aws_iam_policy.ssm_session_manager.arn,
    AmazonSSMManagedInstanceCore    = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore",
    AmazonSSMDirectoryServiceAccess = "arn:aws:iam::aws:policy/AmazonSSMDirectoryServiceAccess",
    S3Access                        = aws_iam_policy.crushftp_s3_access.arn
  }
  block_device_mappings = [
    {
      device_name = "/dev/sda1"
      ebs = {
        delete_on_termination = true
        encrypted             = true
        volume_size           = 50
        volume_type           = "gp3"
      }
    }
  ]

  metadata_options = {
    http_endpoint               = "enabled"
    http_tokens                 = "required"
    http_put_response_hop_limit = 1
  }

  network_interfaces = [
    {
      associate_public_ip_address = false
      delete_on_termination       = true
      description                 = "eth0"
      device_index                = 0
      security_groups = [
        module.security_group_crushftp_egress.security_group_id,
        module.security_group_crushftp_ingress.security_group_id
      ]

    }
  ]

  tag_specifications = [
    {
      resource_type = "instance"
      tags          = { CrushFTPDevAdDomainJoin = "true" }
    }
  ]

  tags = merge(local.resource_tags, {
    Name = "crushftp-dev"
  })

}
