resource "aws_iam_policy" "ssm_session_manager" {
  name        = "${local.resource_name}-ssm-session-manager"
  description = "Provides required permissions for SSM Session Manager"
  path        = "/"
  policy      = data.aws_iam_policy_document.ssm_session_manager.json
  tags        = local.resource_tags
}


data "aws_iam_policy_document" "ssm_session_manager" {
  statement {
    actions = [
      "ssm:UpdateInstanceInformation",
      "ssmmessages:CreateControlChannel",
      "ssmmessages:CreateDataChannel",
      "ssmmessages:OpenControlChannel",
      "ssmmessages:OpenDataChannel",
      "ssm:GetParameter",
      "ssm:StartAutomationExecution",
      "ssm:SendCommand",
      "ecr:GetAuthorizationToken"
    ]

    resources = [
      "*"
    ]
  }
}

resource "aws_iam_policy" "crushftp_s3_access" {
  name        = "CrushFtpDevS3Access"
  description = "S3 access for CrushFTP"
  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Action = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:DeleteObject",
          "s3:AbortMultipartUpload",
          "s3:GetBucketLocation",
          "s3:ListBucket",
          "s3:ListBucketMultipartUploads",
          "s3:ListMultipartUploadParts"
        ],
        Resource = [
          "arn:aws:s3:::haven-dev-ownership-owners-document-legacy-statements",
          "arn:aws:s3:::haven-dev-ownership-owners-document-legacy-statements/*"
        ]
      }
    ]
  })
}