module "vpc" {
  source  = "terraform-aws-modules/vpc/aws"
  version = "5.16.0"

  name = local.vpc_name
  cidr = local.vpc_cidr

  #Added variables to limit the changes to the VPC's
  map_public_ip_on_launch       = local.map_public_ip_on_launch
  manage_default_security_group = local.manage_default_security_group
  manage_default_route_table    = local.manage_default_route_table
  manage_default_network_acl    = local.manage_default_network_acl

  azs             = local.azs
  public_subnets  = local.public_subnets
  private_subnets = local.private_subnets

  enable_ipv6 = false

  enable_nat_gateway = true
  single_nat_gateway = true

  enable_dns_hostnames = true
  enable_dns_support   = true


  enable_vpn_gateway = true

  enable_flow_log                                 = true
  flow_log_traffic_type                           = "REJECT"
  create_flow_log_cloudwatch_iam_role             = true
  create_flow_log_cloudwatch_log_group            = true
  flow_log_cloudwatch_log_group_retention_in_days = 7

}

module "vpc_endpoints" {
  source  = "terraform-aws-modules/vpc/aws//modules/vpc-endpoints"
  version = "5.16.0"

  vpc_id             = module.vpc.vpc_id
  security_group_ids = [module.security_group_internal_any.security_group_id]

  endpoints = {
    s3-gateway = {
      service         = "s3"
      service_type    = "Gateway"
      route_table_ids = flatten([module.vpc.intra_route_table_ids, module.vpc.private_route_table_ids, module.vpc.public_route_table_ids])
      tags = {
        "Name"             = "${var.module_name}-s3-gateway-endpoint"
        "blg:module"       = var.module_name
        "blg:risk"         = "high"
        "blg:serviceowner" = "platform"
      }
    }
    s3-interface = {
      service             = "s3"
      private_dns_enabled = true
      subnet_ids          = module.vpc.private_subnets
      tags = {
        "Name"             = "${var.module_name}-s3-interface-endpoint"
        "blg:module"       = var.module_name
        "blg:risk"         = "high"
        "blg:serviceowner" = "platform"
      }
    }
    ec2 = {
      service             = "ec2"
      private_dns_enabled = true
      subnet_ids          = module.vpc.private_subnets
      tags = {
        "Name"             = "${var.module_name}-ec2-interface-endpoint"
        "blg:module"       = var.module_name
        "blg:risk"         = "high"
        "blg:serviceowner" = "platform"
      }
    }
    ec2messages = {
      service             = "ec2messages"
      private_dns_enabled = true
      subnet_ids          = module.vpc.private_subnets
      tags = {
        "Name"             = "${var.module_name}-ec2mesages-interface-endpoint"
        "blg:module"       = var.module_name
        "blg:risk"         = "high"
        "blg:serviceowner" = "platform"
      }
    }
    ecr-dkr = {
      service             = "ecr.dkr"
      private_dns_enabled = true
      subnet_ids          = module.vpc.private_subnets
      tags = {
        "Name"             = "${var.module_name}-ecr-dkr-interface-endpoint"
        "blg:module"       = var.module_name
        "blg:risk"         = "high"
        "blg:serviceowner" = "platform"
      }
    }
    ecr-api = {
      service             = "ecr.api"
      private_dns_enabled = true
      subnet_ids          = module.vpc.private_subnets
      tags = {
        "Name"             = "${var.module_name}-ecr-api-interface-endpoint"
        "blg:module"       = var.module_name
        "blg:risk"         = "high"
        "blg:serviceowner" = "platform"
      }
    }
    secrets = {
      service             = "secretsmanager"
      private_dns_enabled = true
      subnet_ids          = module.vpc.private_subnets
      tags = {
        "Name"             = "${var.module_name}-secretsmanager-interface-endpoint"
        "blg:module"       = var.module_name
        "blg:risk"         = "high"
        "blg:serviceowner" = "platform"
      }
    }
    ssm = {
      service             = "ssm"
      private_dns_enabled = true
      subnet_ids          = module.vpc.private_subnets
      tags = {
        "Name"             = "${var.module_name}-ssm-interface-endpoint"
        "blg:module"       = var.module_name
        "blg:risk"         = "high"
        "blg:serviceowner" = "platform"
      }
    }
    ssmmessages = {
      service             = "ssmmessages"
      private_dns_enabled = true
      subnet_ids          = module.vpc.private_subnets
      tags = {
        "Name"             = "${var.module_name}-ssmmessages-interface-endpoint"
        "blg:module"       = var.module_name
        "blg:risk"         = "high"
        "blg:serviceowner" = "platform"
      }
    }
  }
}